﻿#include "externs.h"
#include "version.h"

WNDPROC DefaultListCtrlProc, DefaultListViewProc;
int iItem;

HWND hDialog, hEdit, hCEdit, hReport;
HINSTANCE hInst;
std::vector<std::wstring> entries;												// The "groups" of variables displayed on the left list
std::vector<Variable> variables;												// All variables read in from file, e.g. carjacktransform
std::vector<std::pair<uint32_t, uint32_t>> indextable;							// Holds indices of the currently selected group
std::vector<std::pair<std::pair<std::wstring, bool>, std::string>> locations;	// Holds all predefined locations, used by the teleport dialog and map. <<Name, IsMapRelevant>, Bin>
std::vector<CarPart> carparts;													// This vector is filled when bolt report is opened. Contains all the carparts found
std::vector<Item> itemTypes;													// Types of items in the game, e.g. sausage, beer etc
std::vector<ItemAttribute> itemAttributes;										// Attributes items can have, e.g. transform, consumed etc
std::vector<SpecialCase> partSCs;												// Bolt report special cases
std::vector<std::wstring> partIdentifiers;										// Properties of car parts. Used by the bolt report to detect parts properly
std::vector<CarProperty> carproperties;											// Car properties with min and max values such as wear, fuel and other liquids
std::vector<TimetableEntry> timetableEntries;									// Entry for the timetable inside the time and weather dialog
std::vector<std::pair<std::wstring, std::wstring>> carpartsTranslation;		// Car parts translation mapping from English to Chinese
std::vector<std::pair<std::wstring, std::wstring>> vehiclesTranslation;		// Vehicles translation mapping from English to Chinese
std::vector<std::pair<std::wstring, std::wstring>> teleportObjectsTranslation;	// Teleport objects translation mapping from English to Chinese
std::wstring filepath;															// Full file path of currently opened file
std::wstring filename;															// Filename of currently opened file
std::wstring appfolderpath;														// Path to the writable apps folder
HANDLE hTempFile = INVALID_HANDLE_VALUE;										// Handle of the tempfile. Invalidated upon exiting
SYSTEMTIME filedate;
HFONT hListFont;
DebugOutput *dbglog;
#ifdef _MAP
class MapDialog* EditorMap = NULL;
#endif /*_MAP*/

bool bFiledateinit = FALSE, bMakeBackup = TRUE, bEulerAngles = FALSE, bCheckForUpdate = TRUE, bBackupChangeNotified = FALSE, bFirstStartup = TRUE, bAllowScale = FALSE, bDisplayRawNames = FALSE, bCheckIssues = FALSE, bStartWithMap = FALSE;
const std::wstring settings[] = { L"make_backup", L"backup_change_notified", L"check_updates", L"first_startup", L"allow_scale", L"use_euler", L"raw_names", L"check_issues", L"start_with_map"};
PVOID pResizeState = NULL;

const float kindasmall = 1.0e-4f;
const float pi = std::atan(1.f) * 4.f;
const float rad2deg = 180.f / pi;
const float deg2Rad = pi / 180.f;

const std::wstring posInfinity(1, wchar_t(0x221E));
const std::wstring negInfinity = L"-" + posInfinity;
const std::wstring Version = std::to_wstring(VERSION_MAJOR) + L"." + std::to_wstring(VERSION_MINOR);
const std::wstring bools[2] = { L"false", L"true" };
const std::wstring Title = L"MSCEditor " + Version;
const std::wstring IniFile = L"msce.ini";
const std::wstring IniFileCN = L"msce_cn.ini";
const std::wstring ErrorTitle = L"错误!";
const std::wstring HtmlHeader = L"<!DOCTYPE html>\n<html>\n<head>\n<style>\n#q{\nfont-family:\"Consolas\";\nborder-collapse:collapse;\ntransform:translateX(15px);\n}\n#q td, #q th{\nborder: 2px solid #fff;\npadding: 1px 10px;\n}\n#q tr:nth-child(even){background-color:#f2f2f2;}\n#q tr:nth-child(odd){background-color:#e0e0e0;}\n#q tr:hover{background-color:#fff;}\n#q th{\npadding-top:12px;\npadding-bottom:12px;\ntext-align:left;\nbackground-color:#fc4979;\ncolor:white;\n}\n</style>\n</head>\n<body style=\"background-color:#262633;\">\n";
const std::wstring HtmlTableHeader = L"<h1 style=\"color:#fff\">%s</h1>\n<table id = \"q\">\n<tr>\n<th>变量名称</th>\n<th>当前文件的值</th>\n<th>另一个文件的值</th>\n</tr>\n";
const std::wstring HtmlTableEntry = L"<tr>\n<td>%s</td>\n<td>%s</td>\n<td>%s</td>\n</tr>\n";
const std::wstring HtmlEnd = L"</body>\n</html>";


const std::wstring GLOB_STRS[] =
{
	L"输入不是有效的浮点数!", //0
	L"输入不是有效的数组!", //1
	L"数组包含无效的浮点数!", //2
	L"输入不能为负数!", //3
	L"数组只能包含0到1之间的值!", //4
	L"数组只能包含-1到1之间的值!", //5
	L"无法检索文件日期。运行时轮询已禁用。当外部源在运行时修改时，保存文件可能会损坏!", //6
	L"\"%s\" 已被修改。\n\n保存更改?", //7
	L"输入必须是浮点类型，例如 '1.25'", //8
	L"\n找不到条目的开始。预期符号: " + std::wstring(1, HX_STARTENTRY), //9   
	L"已加载 %d 个条目，共 %d 组", //10
	L"%d 个未保存的更改", //11
	L"保存失败: ", //12
	L"\n文件流失败!", //13
	L"无法重命名文件!", //14
	L"无法删除文件!", //15
	L"输出流失败!", //16
	L"\"%s\"\n\n该文件已被另一程序修改 - 很可能是游戏。您是否要加载外部更改?\n\n点击是将丢弃在MSCeditor中所做的所有未保存的更改并重新加载(推荐)。", //17
	L"螺栓总数: %d", //18
	L"固定的螺栓: %d", //19
	L"松动的螺栓: %d", //20
	L"已安装零件: %d / %d", //21
	L"已修复零件: %d", //22
	L"松动零件: %d", //23
	L"损坏零件: %d", //24
	L"卡住零件: %d", //25
	L"无法加载标识符。请确保" + IniFile + L"或" + IniFileCN + L"与可执行文件在同一文件夹中。", //26
	L"旋转四元数 (x , y , z , w)", //27
	L"旋转角度 (俯仰, 偏航, 滚转)", //28
	L"输入包含无效的角度", //29
	L"尝试写入转储文件时出错。", //30
	L"在偏移量处加载失败: ", //31
	L"\n预期为整数，但没有获得任何值!", //32
	L"\n条目意外结束。预期符号: " + std::wstring(1, HX_ENDENTRY), //33    
	L"\n没有条目!", //34
	L"找不到文件 \"" + IniFile + L"\" 或 \"" + IniFileCN + L"\"!\n程序将以减少的功能启动。\n\n\n可能的解决方案:\n\n - 确保文件与此程序在同一文件夹中。\n - 在启动前解压缩压缩档案。\n - 如果文件丢失，请重新下载程序。\n - 以管理员身份运行程序", //35
	L"更新可用! 现在更新?\n(将在浏览器中开始下载)\n\n更新日志:\n", //36
	L"无法写入应用程序文件夹，路径:\n\"%s\"\n请报告此问题。", //37
	L"您的保存文件中有 %d 个问题，可能导致游戏中出现错误。现在检查它们?\n (您可以在保存前查看更改)", //38
	L"无法重新加载文件，因为它已被重命名或删除!", //39
	L"成功写入转储文件!", //40
	L"只是提醒一下，从1.04版本开始，\n设置在启动时不再重置!\n请记住这一点。", //41
	L"嘿，伙计! 看起来你是新用户。", //42
	L"\n意外的文件结束!", //43
	L"您即将打开一个备份文件。这是有意的吗?", //44
	L"\n点击一个值进行修改，然后按设置应用更改或按修复将其设置为推荐值。\n\n\n程序员备注:\n调整零件的值仅仅是我的建议。\n例如，建议的空气/燃料比为14:7，这是化学计量混合比，提供了功率和燃油经济性之间的最佳平衡。要进一步降低燃油消耗，可以使混合物更加稀薄。对于最大功率，您可以将其设置为约13.1。\n\n分电器上的火花正时也是如此。这没有最佳值，因为使用带有N2O的赛车化油器时，正时需要高得多(约13)，而使用双或标准化油器时则较低(约14.8)。\n\n最终齿轮比应在3.7-4.625之间。较低的值提供更高的最高速度但加速性能较低。\n\n\n不过，我强烈建议在游戏中进行调整，因为这就是游戏的意义所在 ;)\n玩得开心!", //45
	L"无法完成操作。\n", //46
	L"找不到物品ID条目!\n", //47
	L"这将删除 \"%s\" 且无法撤消。您确定吗?\n", //48
	L"容器已损坏，无法打开。\n捕获的异常:\n", //49
	L"发现 %d 个问题", //50
	L"个问题", //51
	L"个问题", //52
	L"左键点击: 拖动导航地图。\n鼠标滚轮: 缩放地图。\n右键点击: 操作。", //53
	L"选择:", //54
	L"无法打开地图，因为应用程序文件夹不可写入!", //55
	L"将坐标复制到剪贴板", //56
	L"将对象传送到这里", // 57
	L"测量距离", // 58
	L"到这里的距离", // 59
	L"清除测量", // 60
	L"正在加载1995年...", // 61
	L"地图失败: %s\n请报告此问题!", // 62
	L"结果文件保存在 \"%s\"\n现在在浏览器中打开?", // 63
	L"已标记 %d 个已消耗物品待删除。\n修复了 %d 个物品标识符。\n总共编辑了 %d 个条目。\n(灰色条目将在保存时被删除)", // 64
	L"无法找到物品 \"%s\" 的标识符变量!\n请报告此问题。", // 65
	L"已为 %d 个零件安装电路。\n您可以在主对话框上查看更改。\n\n%s", // 66
	L"无法安装电路，因为它需要安装 \"%s\"。" // 66
};

const std::wstring BListSymbols[] =
{
	{ 0x002D },
	{ 0x2713 },
	{ 0x003F }
};

// Keep this alphabetical
const std::vector<std::pair<std::wstring, std::wstring>> NameTable =
{
	{ L"batteryterminal", L"wiringbattery" },
	{ L"crankbearing", L"mainbearing" },
	{ L"crankwheel", L"crankshaftpulley" },
	{ L"cd1transform", L"cd1" },
	{ L"coffeecuppos", L"coffeecup" },
	{ L"coffeepanpos", L"coffeepan" },
	{ L"computerorderpos", L"computerorder" },
	{ L"fenderflarerf", L"fenderflarerl" },
	{ L"fireextinguisherholder", L"extinguisherholder" },
	{ L"fishtraptransform", L"fishtrap" },
	{ L"floppy1pos", L"floppy1" },
	{ L"floppy2pos", L"floppy2" },
	{ L"floppy3pos", L"floppy3" },
	{ L"gaugeclock", L"clockgauge" },
	{ L"gaugerpm", L"rpmgauge" },
	{ L"headlightleft1", L"headlightleft" },
	{ L"headlightright1", L"headlightright" },
	{ L"ikwishbone", L"wishbone" },
	{ L"kiljubuckettransform", L"kiljubucket" },
	{ L"motorhoisttransform", L"motorhoist" },
	{ L"playertransform", L"player" },
	{ L"rallywheel", L"rallysteeringwheel" },
	{ L"repairshoporder", L"repairshop" },
	{ L"shockrallyfl", L"strutrallyfl" },
	{ L"shockrallyfr", L"strutrallyfr" },
	{ L"shockrallyrl", L"shockabsorberrallyrl" },
	{ L"shockrallyrr", L"shockabsorberrallyrr" },
	{ L"shockfl", L"strutfl" },
	{ L"shockfr", L"strutfr" },
	{ L"shockrl", L"shockabsorberrl" },
	{ L"shockrr", L"shockabsorberrr" },
	{ L"sportwheel", L"sportsteeringwheel" },
	{ L"stocksteeringwheel", L"steeringwheel" },
	{ L"strutflrally", L"strutrallyfl" },
	{ L"strutfrrally", L"strutrallyfr" },
	{ L"valvecover", L"rockercover" },

	{ L"wheelgt1transform", L"wheelgt1" },
	{ L"wheelgt2transform", L"wheelgt2" },
	{ L"wheelgt3transform", L"wheelgt3" },
	{ L"wheelgt4transform", L"wheelgt4" },

	{ L"wheelhayosiko1transform", L"wheelhayosiko1" },
	{ L"wheelhayosiko2transform", L"wheelhayosiko2" },
	{ L"wheelhayosiko3transform", L"wheelhayosiko3" },
	{ L"wheelhayosiko4transform", L"wheelhayosiko4" },

	{ L"wheelocto1transform", L"wheelocto1" },
	{ L"wheelocto2transform", L"wheelocto2" },
	{ L"wheelocto3transform", L"wheelocto3" },
	{ L"wheelocto4transform", L"wheelocto4" },

	{ L"wheelracing1transform", L"wheelracing1" },
	{ L"wheelracing2transform", L"wheelracing2" },
	{ L"wheelracing3transform", L"wheelracing3" },
	{ L"wheelracing4transform", L"wheelracing4" },

	{ L"wheelrally1transform", L"wheelrally1" },
	{ L"wheelrally2transform", L"wheelrally2" },
	{ L"wheelrally3transform", L"wheelrally3" },
	{ L"wheelrally4transform", L"wheelrally4" },

	{ L"wheelslot1transform", L"wheelslot1" },
	{ L"wheelslot2transform", L"wheelslot2" },
	{ L"wheelslot3transform", L"wheelslot3" },
	{ L"wheelslot4transform", L"wheelslot4" },

	{ L"wheelspoke1transform", L"wheelspoke1" },
	{ L"wheelspoke2transform", L"wheelspoke2" },
	{ L"wheelspoke3transform", L"wheelspoke3" },
	{ L"wheelspoke4transform", L"wheelspoke4" },

	{ L"wheelsteel1transform", L"wheelsteel1" },
	{ L"wheelsteel2transform", L"wheelsteel2" },
	{ L"wheelsteel3transform", L"wheelsteel3" },
	{ L"wheelsteel4transform", L"wheelsteel4" },
	{ L"wheelsteel5transform", L"wheelsteel5" },

	{ L"wheelsteelwide1transform", L"wheelsteelwide1" },
	{ L"wheelsteelwide2transform", L"wheelsteelwide2" },
	{ L"wheelsteelwide3transform", L"wheelsteelwide3" },
	{ L"wheelsteelwide4transform", L"wheelsteelwide4" },

	{ L"wheelturbine1transform", L"wheelturbine1" },
	{ L"wheelturbine2transform", L"wheelturbine2" },
	{ L"wheelturbine3transform", L"wheelturbine3" },
	{ L"wheelturbine4transform", L"wheelturbine4" },

	{ L"wiringmesstransform", L"wiring" },
};