## 核心设计需求
设计一个符合苹果美学风格的现代化桌面应用程序界面，重点突出以下特征：

### 1. 视觉风格
- **色彩方案**：
  - 主背景：纯白色（#FFFFFF）
  - 辅助色：浅灰色（#F5F5F7）用于分隔区域
  - 强调色：使用iOS系统蓝（#007AFF）作为主要交互色
  - 文本色：深灰（#1D1D1F）主文本，中灰（#86868B）次要文本

- **材质效果**：
  - 半透明亚克力效果（关键区域使用背景模糊）
  - 极细分割线（1px，#E5E5EA）
  - 柔和阴影（仅用于悬浮卡片，8px模糊度）

### 2. 界面元素
- **窗口特性**：
  - 无边框设计（或极细1px边框）
  - 符合WIN10/11风格的标题栏控制按钮
  - 动态圆角（窗口4-8px，内部元素统一6px）

- **控件规范**：
  - 按钮：填充式（主操作）+ 描边式（次要操作）
  - 输入框：聚焦状态显示蓝色光晕
  - 选择器：iOS风格的滚轮式选择

### 3. 交互设计
- **动效原则**：
  - 所有过渡动画时长控制在0.3s
  - 使用缓动曲线（cubic-bezier(0.4,0,0.2,1)）
  - 按钮点击伴随0.95缩放效果

- **反馈机制**：
  - 操作成功显示短暂HUD提示
  - 错误状态使用红色（#FF3B30）脉冲动画
  - 加载状态使用线性进度指示器


### 排除项说明
- 不包含深色/浅色主题切换功能
- 不强制要求特定字体（但需保证可读性）
- 不使用拟物化设计元素

## 补充说明
实际开发时应特别注意：
1. 确保模糊效果在Windows平台的性能表现
2. 控件尺寸需同时满足Windows的点击区域要求
3. 系统级弹窗需保持与原生风格协调