//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by MSCeditor.rc
//
#define VER_VER_DEBUG                           0
#define VERSION_MAJOR                           1
#define VERSION_MINOR                           12
#define IDD_MAIN                                101
#define IDD_TRANSFORM                           103
#define IDD_COLOR                               107
#define IDR_MENU                                109
#define IDI_ICON1                               110
#define IDD_ABOUT                               116
#define IDD_DIALOG1                             119
#define IDD_STRING                              121
#define IDD_TELEPORT                            122
#define IDD_BOLTS                               124
#define IDD_DIALOG2                             128
#define IDD_SPAWNITEM                           128
#define IDD_CLEANITEMS                          131
#define IDD_HELP                                132
#define IDD_REPORT                              133
#define IDD_DIALOG3                             135
#define IDD_MAINTENANCE                         135
#define IDD_VECTOR                              140
#define IDD_KEYS                                143
#define IDD_BLANK                               145
#define IDD_TIMEWEATHER                         147
#define IDD_CONTAINER                           148
#define IDD_MAP                                 152
#define IDR_MAPJPG                              154
#define IDD_BLANKMAP                            155
#define IDR_CONFIG_INI                          156
#define IDD_COMPAREOPTIONS                      161
#define IDD_LIYUE                               162
#define IDC_List                                1002
#define IDC_FILTER                              1003
#define IDC_List2                               1008
#define IDC_LOC                                 1010
#define IDC_ROT                                 1013
#define IDC_TAG                                 1014
#define IDC_SCA                                 1016
#define IDC_APPLY                               1019
#define IDC_DISCARD                             1020
#define IDC_COL                                 1021
#define IDC_APPLY2                              1021
#define IDC_OUTPUTBAR1                          1022
#define IDC_OUTPUTBAR2                          1023
#define IDC_OUTPUT1                             1024
#define IDC_OUTPUT2                             1025
#define IDC_OUTPUTBAR3                          1026
#define IDC_STRINGEDIT                          1027
#define IDC_OFFSET                              1028
#define IDC_OUTPUTBAR4                          1031
#define IDC_OUTPUT3                             1032
#define IDC_OUTPUT4                             1033
#define IDC_MFCLINK1                            1034
#define IDC_CICON                               1035
#define IDC_VERSION                             1036
#define IDC_LOCFROM                             1037
#define IDC_LOCTO                               1038
#define IDC_TELEC                               1039
#define IDC_BLIST                               1040
#define IDC_STXT1                               1041
#define IDC_STXT2                               1042
#define IDC_FEEDBACK                            1043
#define IDC_BT1                                 1044
#define IDC_BT2                                 1045
#define IDC_BT3                                 1046
#define IDC_BBUT1                               1047
#define IDC_ROTBOX                              1048
#define IDC_BUTTON1                             1049
#define IDC_BT4                                 1050
#define IDC_BT5                                 1051
#define IDC_BT6                                 1052
#define IDC_SPAWNWHAT                           1052
#define IDC_BT7                                 1053
#define IDC_BT8                                 1054
#define IDC_BBUT3                               1056
#define IDC_AMOUNT                              1056
#define IDC_BBUT2                               1057
#define IDC_CLEANWHAT                           1057
#define IDC_BBUT4                               1058
#define IDC_IT                                  1058
#define IDC_BBUT5                               1059
#define IDC_BBUT6                               1060
#define IDC_HELPNEW                             1060
#define IDC_DISCARDTEXT                         1062
#define IDC_CHECK1                              1063
#define IDC_COMP_BOX_TR                         1063
#define IDC_PLEDIT                              1064
#define IDC_COMP_BOX_FL                         1064
#define IDC_PLSET                               1065
#define IDC_COMP_BOX_ST                         1065
#define IDC_BUTNEGINF                           1066
#define IDC_COMP_BOX_BO                         1066
#define IDC_BUTPOSINF                           1067
#define IDC_COMP_BOX_CO                         1067
#define IDC_COMP_BOX_IN                         1068
#define IDC_TTIME                               1069
#define IDC_COMP_BOX_VE                         1069
#define IDC_TWEATHER                            1070
#define IDC_CHECK8                              1070
#define IDC_COMP_BOX_UN                         1070
#define IDC_TDAY                                1071
#define IDC_TTIMETABLE                          1072
#define IDC_LIST1                               1073
#define IDC_CONTAINERLIST                       1073
#define IDC_EDIT1                               1074
#define IDC_COMP_EDIT_FI                        1074
#define IDC_COMP_BOX_DT                         1075
#define IDC_COMP_EDIT_FL                        1076
#define IDC_LIYUE_TEXT                          1077
#define ID_PL_BASE_OFFSET                       10000
#define ID_FILE_EXIT                            40002
#define ID_TOOLS_TELEPORTENTITY                 40003
#define ID_TOOLS_REPORT                         40004
#define ID__HELP                                40005
#define ID__ABOUT                               40006
#define ID__LIYUE                               40035
#define ID_FILE_OPEN                            40008
#define ID_FILE_SAVE                            40009
#define ID_MAP_CLEAR_MEASURE                    40011
#define ID_FILE_CLOSE                           40012
#define ID_FILE_EXPLORER                        40013
#define ID_FILE_GAME                            40014
#define ID_OPTIONS_MAKEBACKUP                   40015
#define ID_OPTIONS_USEEULERANGLES               40016
#define ID_TOOLS_COMPARE                        40017
#define ID_TOOLS_TESTLOL                        40018
#define ID_RESET                                40019
#define ID_DELETE                               40020
#define ID_FILE_GAMESTEAM                       40021
#define ID_ITEMS_SPAWNITEMS                     40022
#define ID_SHOW_ON_MAP                          40023
#define ID_ITEMS_CLEANUPITEMS                   40024
#define ID_MAP_CLIPBOARD                        40025
#define ID_MAP_TELEPORT                         40026
#define ID_MAP_MEASURE_DISTANCE                 40027
#define ID_OPTIONS_CHECKFORUPDATES              40028
#define ID_TOOLS_MANAGEKEYS                     40029
#define ID_TOOLS_TIMEANDWEATHER                 40030
#define ID_OPTIONS_DISPLAYRAWNAMES              40031
#define ID_TOOLS_ISSUEDIAGNOSTICS               40032
#define ID_TOOLS_WORLDMAP                       40033
#define ID_OPTIONS_CHECKISSUESWHENSAVING        40034

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE                163
#define _APS_NEXT_COMMAND_VALUE                 40036
#define _APS_NEXT_CONTROL_VALUE                 1078
#define _APS_NEXT_SYMED_VALUE                   101
#endif
#endif
