﻿  AppleUI.cpp
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(260,106): error C2065: “AppleDialogProc”: 未声明的标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(260,122): error C2660: “SetWindowLongPtrW”: 函数不接受 2 个参数
F:\Windows Kits\10\Include\10.0.20348.0\um\winuser.h(9851,1): message : 参见“SetWindowLongPtrW”的声明
  main.cpp
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(666,8): error C2196: case 值“312”已使用
