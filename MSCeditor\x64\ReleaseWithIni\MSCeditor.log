﻿  AppleUI.cpp
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(239,24): error C2065: “EM_SETBKGNDCOLOR”: 未声明的标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(542,59): warning C4311: “类型强制转换”: 从“HANDLE”到“BOOL”的指针截断
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(542,59): warning C4302: “类型强制转换”: 从“HANDLE”到“BOOL”截断
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(630,38): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\AppleUI.cpp(634,50): warning C4312: “类型强制转换”: 从“BOOL”转换到更大的“HANDLE”
  main.cpp
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(34,4): error C3861: “InitializeAppleUI”: 找不到标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(37,9): error C3861: “TestAppleUISystem”: 找不到标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(96,4): error C3861: “ApplyAppleStyleToListView”: 找不到标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(97,4): error C3861: “ApplyAppleStyleToListView”: 找不到标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(213,4): error C3861: “ApplyAppleStyleToDialog”: 找不到标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(216,4): error C3861: “ApplyAppleWindowStyle”: 找不到标识符
C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp(698,5): error C3861: “CleanupAppleUI”: 找不到标识符
