#include "AppleUI.h"
#include <CommCtrl.h>
#include <math.h>

// ==========================================
// 全局变量定义
// ==========================================

HBRUSH g_hAppleWhiteBrush = NULL;
HBRUSH g_hAppleLightGrayBrush = NULL;
HBRUSH g_hAppleBlueBrush = NULL;
HFONT g_hAppleFont = NULL;
HFONT g_hAppleBoldFont = NULL;

// ==========================================
// 工具函数实现
// ==========================================

HBRUSH CreateAppleBrush(COLORREF color)
{
    return CreateSolidBrush(color);
}

HFONT CreateAppleFont(int size, int weight, BOOL italic)
{
    // 尝试创建最佳的中文字体，按优先级顺序
    const WCHAR* fontNames[] = {
        L"Microsoft YaHei UI",      // 微软雅黑UI - 最现代
        L"Microsoft YaHei",         // 微软雅黑 - 备选
        L"Segoe UI",               // Segoe UI - Windows现代字体
        L"PingFang SC",            // 苹果中文字体（如果有）
        L"Source Han Sans SC",     // 思源黑体简体
        L"Noto Sans CJK SC",       // Google Noto字体
        L"SimHei",                 // 黑体 - 传统备选
        L"新宋体"                   // 新宋体 - 最后备选
    };

    HFONT hFont = NULL;

    // 尝试每种字体
    for (int i = 0; i < sizeof(fontNames) / sizeof(fontNames[0]); i++)
    {
        hFont = CreateFont(
            size, 0, 0, 0,
            weight,
            italic,
            FALSE, 0,
            GB2312_CHARSET,  // 保持中文支持
            OUT_DEFAULT_PRECIS,
            CLIP_DEFAULT_PRECIS,
            CLEARTYPE_QUALITY,  // 使用ClearType提高渲染质量
            DEFAULT_PITCH | FF_DONTCARE,
            fontNames[i]
        );

        if (hFont)
        {
            // 验证字体是否真正可用
            HDC hdc = GetDC(NULL);
            HFONT oldFont = (HFONT)SelectObject(hdc, hFont);

            TEXTMETRIC tm;
            GetTextMetrics(hdc, &tm);

            // 检查字体名称是否匹配（确保不是替代字体）
            WCHAR actualFontName[LF_FACESIZE];
            GetTextFace(hdc, LF_FACESIZE, actualFontName);

            SelectObject(hdc, oldFont);
            ReleaseDC(NULL, hdc);

            // 如果字体名称匹配或者是最后一个选项，使用这个字体
            if (wcscmp(actualFontName, fontNames[i]) == 0 || i == sizeof(fontNames) / sizeof(fontNames[0]) - 1)
            {
                break;
            }
            else
            {
                // 字体被替代了，删除并尝试下一个
                DeleteObject(hFont);
                hFont = NULL;
            }
        }
    }

    return hFont;
}

void SetOptimalTextRenderingMode(HDC hdc)
{
    // 设置文本对齐方式
    SetTextAlign(hdc, TA_LEFT | TA_TOP);

    // 启用透明背景
    SetBkMode(hdc, TRANSPARENT);

    // 设置字符间距（如果需要）
    SetTextCharacterExtra(hdc, 0);

    // 设置文本质量（Windows API）
    SetMapMode(hdc, MM_TEXT);
}

void DrawRoundedRect(HDC hdc, RECT* rect, int radius, COLORREF fillColor, COLORREF borderColor, int borderWidth)
{
    // 创建圆角区域
    HRGN hRgn = CreateRoundRectRgn(rect->left, rect->top, rect->right, rect->bottom, radius * 2, radius * 2);
    
    // 填充背景
    HBRUSH hBrush = CreateSolidBrush(fillColor);
    FillRgn(hdc, hRgn, hBrush);
    DeleteObject(hBrush);
    
    // 绘制边框
    if (borderWidth > 0 && borderColor != 0)
    {
        HPEN hPen = CreatePen(PS_SOLID, borderWidth, borderColor);
        HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);
        
        SelectClipRgn(hdc, hRgn);
        Rectangle(hdc, rect->left, rect->top, rect->right, rect->bottom);
        SelectClipRgn(hdc, NULL);
        
        SelectObject(hdc, hOldPen);
        DeleteObject(hPen);
    }
    
    DeleteObject(hRgn);
}

void DrawAppleButton(HDC hdc, RECT* rect, LPCWSTR text, AppleButtonStyle style, BOOL isHovered, BOOL isPressed)
{
    COLORREF fillColor, textColor, borderColor = 0;
    int borderWidth = 0;
    
    // 根据样式和状态确定颜色
    switch (style)
    {
        case APPLE_BUTTON_PRIMARY:
            fillColor = isPressed ? APPLE_BLUE_PRESSED : (isHovered ? APPLE_BLUE_HOVER : APPLE_BLUE);
            textColor = APPLE_WHITE;
            break;
            
        case APPLE_BUTTON_SECONDARY:
            fillColor = isPressed ? APPLE_GRAY_HOVER : (isHovered ? APPLE_GRAY_HOVER : APPLE_WHITE);
            textColor = APPLE_BLUE;
            borderColor = APPLE_SEPARATOR;
            borderWidth = 1;
            break;
            
        case APPLE_BUTTON_TERTIARY:
            fillColor = isPressed ? APPLE_GRAY_HOVER : (isHovered ? APPLE_GRAY_HOVER : APPLE_WHITE);
            textColor = APPLE_BLUE;
            break;
    }
    
    // 绘制按钮背景
    DrawRoundedRect(hdc, rect, APPLE_CORNER_RADIUS_MEDIUM, fillColor, borderColor, borderWidth);
    
    // 绘制文本
    if (text && wcslen(text) > 0)
    {
        SetOptimalTextRenderingMode(hdc);
        SetTextColor(hdc, textColor);
        SetBkMode(hdc, TRANSPARENT);

        HFONT hOldFont = (HFONT)SelectObject(hdc, g_hAppleFont);
        DrawText(hdc, text, -1, rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
        SelectObject(hdc, hOldFont);
    }
}

void DrawAppleInput(HDC hdc, RECT* rect, AppleInputStyle style)
{
    COLORREF fillColor = APPLE_WHITE;
    COLORREF borderColor;
    int borderWidth = 1;
    
    switch (style)
    {
        case APPLE_INPUT_NORMAL:
            borderColor = APPLE_SEPARATOR;
            break;
        case APPLE_INPUT_FOCUSED:
            borderColor = APPLE_BLUE;
            borderWidth = 2;
            break;
        case APPLE_INPUT_ERROR:
            borderColor = APPLE_ERROR;
            borderWidth = 2;
            break;
    }
    
    DrawRoundedRect(hdc, rect, APPLE_CORNER_RADIUS_SMALL, fillColor, borderColor, borderWidth);
}

void ApplyAppleStyleToListView(HWND hListView)
{
    // 设置ListView的扩展样式
    DWORD dwExStyle = ListView_GetExtendedListViewStyle(hListView);
    dwExStyle |= LVS_EX_FULLROWSELECT | LVS_EX_DOUBLEBUFFER | LVS_EX_GRIDLINES;
    ListView_SetExtendedListViewStyle(hListView, dwExStyle);

    // 设置背景色
    // 使用浅灰色背景使变化更明显
    ListView_SetBkColor(hListView, APPLE_LIGHT_GRAY);
    ListView_SetTextBkColor(hListView, APPLE_LIGHT_GRAY);
    ListView_SetTextColor(hListView, APPLE_TEXT_PRIMARY);
}

void ApplyAppleStyleToButton(HWND hButton, AppleButtonStyle style)
{
    // 设置按钮字体
    SendMessage(hButton, WM_SETFONT, (WPARAM)g_hAppleFont, TRUE);

    // 设置按钮为自绘模式
    LONG buttonStyle = GetWindowLong(hButton, GWL_STYLE);
    buttonStyle |= BS_OWNERDRAW;
    SetWindowLong(hButton, GWL_STYLE, buttonStyle);

    // 存储按钮样式信息（使用窗口属性）
    SetProp(hButton, L"AppleButtonStyle", (HANDLE)(LONG_PTR)style);
}

void ApplyAppleStyleToEdit(HWND hEdit)
{
    // 设置编辑框字体
    SendMessage(hEdit, WM_SETFONT, (WPARAM)g_hAppleFont, TRUE);

    // 移除边框，我们将自定义绘制
    LONG style = GetWindowLong(hEdit, GWL_STYLE);
    style &= ~WS_BORDER;
    SetWindowLong(hEdit, GWL_STYLE, style);

    LONG exStyle = GetWindowLong(hEdit, GWL_EXSTYLE);
    exStyle &= ~(WS_EX_CLIENTEDGE | WS_EX_STATICEDGE);
    SetWindowLong(hEdit, GWL_EXSTYLE, exStyle);

    // 设置背景色 (注释掉不支持的消息)
    // SendMessage(hEdit, EM_SETBKGNDCOLOR, 0, APPLE_WHITE);
}

void ApplyAppleStyleToStatic(HWND hStatic)
{
    // 设置静态文本字体
    SendMessage(hStatic, WM_SETFONT, (WPARAM)g_hAppleFont, TRUE);

    // 设置透明背景
    LONG style = GetWindowLong(hStatic, GWL_STYLE);
    style |= SS_NOTIFY; // 允许接收鼠标消息
    SetWindowLong(hStatic, GWL_STYLE, style);
}

void ApplyAppleStyleToDialog(HWND hDialog)
{
    // 设置对话框背景色
    SetClassLongPtr(hDialog, GCLP_HBRBACKGROUND, (LONG_PTR)g_hAppleWhiteBrush);

    // 设置对话框的自定义窗口过程来处理背景绘制
    SetWindowLongPtr(hDialog, GWLP_USERDATA, (LONG_PTR)SetWindowLongPtr(hDialog, GWLP_WNDPROC, (LONG_PTR)AppleDialogProc));

    // 遍历所有子控件并应用样式
    EnumChildWindows(hDialog, [](HWND hChild, LPARAM lParam) -> BOOL {
        WCHAR className[256];
        GetClassName(hChild, className, 256);

        if (wcscmp(className, L"SysListView32") == 0)
        {
            ApplyAppleStyleToListView(hChild);
        }
        else if (wcscmp(className, L"Button") == 0)
        {
            // 根据按钮ID确定样式
            int buttonId = GetDlgCtrlID(hChild);
            AppleButtonStyle style = APPLE_BUTTON_PRIMARY;

            // 可以根据按钮ID设置不同样式
            if (buttonId == IDCANCEL || buttonId == IDNO)
                style = APPLE_BUTTON_SECONDARY;
            else if (buttonId == IDOK || buttonId == IDYES)
                style = APPLE_BUTTON_PRIMARY;
            else
                style = APPLE_BUTTON_PRIMARY; // 默认使用主要样式，更明显

            ApplyAppleStyleToButton(hChild, style);
        }
        else if (wcscmp(className, L"Edit") == 0)
        {
            ApplyAppleStyleToEdit(hChild);
        }
        else if (wcscmp(className, L"Static") == 0)
        {
            ApplyAppleStyleToStatic(hChild);
        }

        return TRUE;
    }, 0);

    // 强制重绘整个对话框
    InvalidateRect(hDialog, NULL, TRUE);
    UpdateWindow(hDialog);
}

void ApplyAppleWindowStyle(HWND hWindow)
{
    // 获取当前窗口样式
    LONG style = GetWindowLong(hWindow, GWL_STYLE);
    LONG exStyle = GetWindowLong(hWindow, GWL_EXSTYLE);

    // 移除传统边框，添加现代样式
    style &= ~(WS_BORDER | WS_DLGFRAME);
    exStyle &= ~(WS_EX_DLGMODALFRAME | WS_EX_WINDOWEDGE | WS_EX_STATICEDGE);

    // 添加分层窗口支持（用于透明度和特效）
    exStyle |= WS_EX_LAYERED;

    // 应用新样式
    SetWindowLong(hWindow, GWL_STYLE, style);
    SetWindowLong(hWindow, GWL_EXSTYLE, exStyle);

    // 设置窗口透明度（轻微透明以实现现代感）
    SetLayeredWindowAttributes(hWindow, 0, 250, LWA_ALPHA);

    // 强制重绘窗口
    SetWindowPos(hWindow, NULL, 0, 0, 0, 0,
                 SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER | SWP_FRAMECHANGED);
}

// 存储原始窗口过程
static WNDPROC g_OriginalWindowProc = NULL;

LRESULT CALLBACK AppleWindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
        case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);

            // 获取客户区矩形
            RECT clientRect;
            GetClientRect(hWnd, &clientRect);

            // 绘制Apple风格背景
            HBRUSH hBrush = CreateSolidBrush(APPLE_WHITE);
            FillRect(hdc, &clientRect, hBrush);
            DeleteObject(hBrush);

            EndPaint(hWnd, &ps);
            return 0;
        }

        case WM_NCPAINT:
        {
            // 自定义非客户区绘制
            HDC hdc = GetWindowDC(hWnd);

            RECT windowRect;
            GetWindowRect(hWnd, &windowRect);
            OffsetRect(&windowRect, -windowRect.left, -windowRect.top);

            // 绘制极细边框
            HPEN hPen = CreatePen(PS_SOLID, 1, APPLE_SEPARATOR);
            HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);

            Rectangle(hdc, windowRect.left, windowRect.top,
                     windowRect.right - 1, windowRect.bottom - 1);

            SelectObject(hdc, hOldPen);
            DeleteObject(hPen);

            ReleaseDC(hWnd, hdc);
            return 0;
        }
    }

    // 调用原始窗口过程
    if (g_OriginalWindowProc)
        return CallWindowProc(g_OriginalWindowProc, hWnd, message, wParam, lParam);
    else
        return DefWindowProc(hWnd, message, wParam, lParam);
}

void HandleOwnerDrawButton(LPDRAWITEMSTRUCT lpDrawItem)
{
    if (lpDrawItem->CtlType != ODT_BUTTON)
        return;

    HDC hdc = lpDrawItem->hDC;
    RECT rect = lpDrawItem->rcItem;
    HWND hButton = lpDrawItem->hwndItem;

    // 获取按钮样式
    AppleButtonStyle style = (AppleButtonStyle)(LONG_PTR)GetProp(hButton, L"AppleButtonStyle");

    // 获取按钮状态
    BOOL isPressed = (lpDrawItem->itemState & ODS_SELECTED) != 0;
    BOOL isHovered = (lpDrawItem->itemState & ODS_HOTLIGHT) != 0;
    BOOL isFocused = (lpDrawItem->itemState & ODS_FOCUS) != 0;
    BOOL isDisabled = (lpDrawItem->itemState & ODS_DISABLED) != 0;

    // 获取按钮文本
    WCHAR buttonText[256];
    GetWindowText(hButton, buttonText, 256);

    // 确定颜色
    COLORREF fillColor, textColor, borderColor = 0;
    int borderWidth = 0;

    if (isDisabled)
    {
        fillColor = APPLE_LIGHT_GRAY;
        textColor = APPLE_TEXT_SECONDARY;
        if (style == APPLE_BUTTON_SECONDARY)
        {
            borderColor = APPLE_SEPARATOR;
            borderWidth = 1;
        }
    }
    else
    {
        switch (style)
        {
            case APPLE_BUTTON_PRIMARY:
                fillColor = isPressed ? APPLE_BLUE_PRESSED : (isHovered ? APPLE_BLUE_HOVER : APPLE_BLUE);
                textColor = APPLE_WHITE;
                break;

            case APPLE_BUTTON_SECONDARY:
                fillColor = isPressed ? APPLE_GRAY_HOVER : (isHovered ? APPLE_GRAY_HOVER : APPLE_WHITE);
                textColor = APPLE_BLUE;
                borderColor = APPLE_SEPARATOR;
                borderWidth = 1;
                break;

            case APPLE_BUTTON_TERTIARY:
                fillColor = isPressed ? APPLE_GRAY_HOVER : (isHovered ? APPLE_GRAY_HOVER : APPLE_WHITE);
                textColor = APPLE_BLUE;
                break;
        }
    }

    // 绘制按钮背景
    DrawRoundedRect(hdc, &rect, APPLE_CORNER_RADIUS_MEDIUM, fillColor, borderColor, borderWidth);

    // 绘制焦点指示器
    if (isFocused && !isDisabled)
    {
        RECT focusRect = rect;
        InflateRect(&focusRect, -3, -3);
        HPEN focusPen = CreatePen(PS_DOT, 1, APPLE_BLUE);
        HPEN oldPen = (HPEN)SelectObject(hdc, focusPen);

        RoundRect(hdc, focusRect.left, focusRect.top, focusRect.right, focusRect.bottom,
                  APPLE_CORNER_RADIUS_SMALL, APPLE_CORNER_RADIUS_SMALL);

        SelectObject(hdc, oldPen);
        DeleteObject(focusPen);
    }

    // 绘制按钮文本
    if (wcslen(buttonText) > 0)
    {
        SetOptimalTextRenderingMode(hdc);
        SetTextColor(hdc, textColor);
        SetBkMode(hdc, TRANSPARENT);

        HFONT oldFont = (HFONT)SelectObject(hdc, g_hAppleFont);
        DrawText(hdc, buttonText, -1, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
        SelectObject(hdc, oldFont);
    }
}

// ==========================================
// 动画和交互效果实现
// ==========================================

void StartButtonPressAnimation(HWND hButton)
{
    // 使用Windows动画API实现按钮按下效果
    RECT rect;
    GetWindowRect(hButton, &rect);

    // 计算缩放后的尺寸
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    int newWidth = (int)(width * APPLE_BUTTON_SCALE);
    int newHeight = (int)(height * APPLE_BUTTON_SCALE);
    int offsetX = (width - newWidth) / 2;
    int offsetY = (height - newHeight) / 2;

    // 转换为父窗口坐标
    HWND hParent = GetParent(hButton);
    ScreenToClient(hParent, (LPPOINT)&rect.left);
    ScreenToClient(hParent, (LPPOINT)&rect.right);

    // 设置新位置和大小
    SetWindowPos(hButton, NULL,
                 rect.left + offsetX, rect.top + offsetY,
                 newWidth, newHeight,
                 SWP_NOZORDER | SWP_NOACTIVATE);

    // 强制重绘
    InvalidateRect(hButton, NULL, TRUE);
}

void StartButtonReleaseAnimation(HWND hButton)
{
    // 恢复按钮原始大小
    RECT rect;
    GetWindowRect(hButton, &rect);

    // 计算原始尺寸
    int scaledWidth = rect.right - rect.left;
    int scaledHeight = rect.bottom - rect.top;
    int originalWidth = (int)(scaledWidth / APPLE_BUTTON_SCALE);
    int originalHeight = (int)(scaledHeight / APPLE_BUTTON_SCALE);
    int offsetX = (scaledWidth - originalWidth) / 2;
    int offsetY = (scaledHeight - originalHeight) / 2;

    // 转换为父窗口坐标
    HWND hParent = GetParent(hButton);
    ScreenToClient(hParent, (LPPOINT)&rect.left);
    ScreenToClient(hParent, (LPPOINT)&rect.right);

    // 恢复原始位置和大小
    SetWindowPos(hButton, NULL,
                 rect.left - offsetX, rect.top - offsetY,
                 originalWidth, originalHeight,
                 SWP_NOZORDER | SWP_NOACTIVATE);

    // 强制重绘
    InvalidateRect(hButton, NULL, TRUE);
}

// HUD消息窗口过程
LRESULT CALLBACK HUDWindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
        case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);

            RECT rect;
            GetClientRect(hWnd, &rect);

            // 获取消息文本
            WCHAR* text = (WCHAR*)GetWindowLongPtr(hWnd, GWLP_USERDATA);
            BOOL isError = (GetProp(hWnd, L"IsError") != NULL);

            // 绘制半透明背景
            COLORREF bgColor = isError ? APPLE_ERROR : APPLE_SUCCESS;
            HBRUSH hBrush = CreateSolidBrush(bgColor);
            FillRect(hdc, &rect, hBrush);
            DeleteObject(hBrush);

            // 绘制文本
            if (text)
            {
                SetOptimalTextRenderingMode(hdc);
                SetTextColor(hdc, APPLE_WHITE);
                SetBkMode(hdc, TRANSPARENT);
                HFONT oldFont = (HFONT)SelectObject(hdc, g_hAppleBoldFont);
                DrawText(hdc, text, -1, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
                SelectObject(hdc, oldFont);
            }

            EndPaint(hWnd, &ps);
            return 0;
        }

        case WM_TIMER:
        {
            // 自动关闭HUD
            if (wParam == 1)
            {
                KillTimer(hWnd, 1);
                DestroyWindow(hWnd);
            }
            return 0;
        }

        case WM_DESTROY:
        {
            // 清理资源
            WCHAR* text = (WCHAR*)GetWindowLongPtr(hWnd, GWLP_USERDATA);
            if (text) delete[] text;
            RemoveProp(hWnd, L"IsError");
            return 0;
        }
    }

    return DefWindowProc(hWnd, message, wParam, lParam);
}

void ShowHUDMessage(HWND hParent, LPCWSTR message, BOOL isError)
{
    // 注册HUD窗口类
    static BOOL classRegistered = FALSE;
    if (!classRegistered)
    {
        WNDCLASS wc = {0};
        wc.lpfnWndProc = HUDWindowProc;
        wc.hInstance = GetModuleHandle(NULL);
        wc.lpszClassName = L"AppleHUDWindow";
        wc.hbrBackground = NULL;
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        RegisterClass(&wc);
        classRegistered = TRUE;
    }

    // 获取父窗口中心位置
    RECT parentRect;
    GetClientRect(hParent, &parentRect);

    int hudWidth = 200;
    int hudHeight = 50;
    int x = (parentRect.right - hudWidth) / 2;
    int y = (parentRect.bottom - hudHeight) / 2;

    // 创建HUD窗口
    HWND hHUD = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST,
        L"AppleHUDWindow",
        NULL,
        WS_POPUP,
        x, y, hudWidth, hudHeight,
        hParent,
        NULL,
        GetModuleHandle(NULL),
        NULL
    );

    if (hHUD)
    {
        // 复制消息文本
        int len = (int)(wcslen(message) + 1);
        WCHAR* text = new WCHAR[len];
        wcscpy_s(text, len, message);
        SetWindowLongPtr(hHUD, GWLP_USERDATA, (LONG_PTR)text);
        SetProp(hHUD, L"IsError", (HANDLE)(LONG_PTR)isError);

        // 设置透明度
        SetLayeredWindowAttributes(hHUD, 0, 220, LWA_ALPHA);

        // 显示窗口
        ShowWindow(hHUD, SW_SHOW);

        // 设置自动关闭定时器
        SetTimer(hHUD, 1, 2000, NULL); // 2秒后关闭
    }
}

void StartPulseAnimation(HWND hControl)
{
    // 简单的脉冲效果：改变控件的透明度
    static int pulseCount = 0;

    // 使用定时器实现脉冲效果
    SetTimer(hControl, 100, 100, [](HWND hWnd, UINT, UINT_PTR, DWORD) -> VOID {
        static int alpha = 255;
        static int direction = -1;

        alpha += direction * 20;
        if (alpha <= 100) {
            alpha = 100;
            direction = 1;
        } else if (alpha >= 255) {
            alpha = 255;
            direction = -1;
        }

        // 应用透明度（如果控件支持）
        LONG exStyle = GetWindowLong(hWnd, GWL_EXSTYLE);
        if (!(exStyle & WS_EX_LAYERED)) {
            SetWindowLong(hWnd, GWL_EXSTYLE, exStyle | WS_EX_LAYERED);
        }
        SetLayeredWindowAttributes(hWnd, 0, alpha, LWA_ALPHA);

        // 停止动画条件
        static int count = 0;
        if (++count > 20) { // 2秒后停止
            KillTimer(hWnd, 100);
            SetLayeredWindowAttributes(hWnd, 0, 255, LWA_ALPHA);
            count = 0;
        }
    });
}

// ==========================================
// 初始化和清理函数
// ==========================================

void InitializeAppleUI()
{
    // 创建全局画刷
    g_hAppleWhiteBrush = CreateAppleBrush(APPLE_WHITE);
    g_hAppleLightGrayBrush = CreateAppleBrush(APPLE_LIGHT_GRAY);
    g_hAppleBlueBrush = CreateAppleBrush(APPLE_BLUE);

    // 创建全局字体
    g_hAppleFont = CreateAppleFont(APPLE_FONT_SIZE_NORMAL, APPLE_FONT_WEIGHT_NORMAL);
    g_hAppleBoldFont = CreateAppleFont(APPLE_FONT_SIZE_NORMAL, APPLE_FONT_WEIGHT_BOLD);

    // 验证资源创建是否成功
    if (!g_hAppleFont)
    {
        // 如果创建失败，使用系统默认字体
        g_hAppleFont = (HFONT)GetStockObject(DEFAULT_GUI_FONT);
    }

    if (!g_hAppleBoldFont)
    {
        // 如果创建失败，使用普通字体
        g_hAppleBoldFont = g_hAppleFont;
    }
}

void CleanupAppleUI()
{
    // 清理画刷
    if (g_hAppleWhiteBrush) DeleteObject(g_hAppleWhiteBrush);
    if (g_hAppleLightGrayBrush) DeleteObject(g_hAppleLightGrayBrush);
    if (g_hAppleBlueBrush) DeleteObject(g_hAppleBlueBrush);
    
    // 清理字体
    if (g_hAppleFont && g_hAppleFont != (HFONT)GetStockObject(DEFAULT_GUI_FONT))
        DeleteObject(g_hAppleFont);
    if (g_hAppleBoldFont && g_hAppleBoldFont != g_hAppleFont)
        DeleteObject(g_hAppleBoldFont);
}

BOOL TestAppleUISystem()
{
    // 测试画刷创建
    if (!g_hAppleWhiteBrush || !g_hAppleLightGrayBrush || !g_hAppleBlueBrush)
        return FALSE;

    // 测试字体创建
    if (!g_hAppleFont || !g_hAppleBoldFont)
        return FALSE;

    // 测试字体质量
    HDC hdc = GetDC(NULL);
    if (hdc)
    {
        HFONT oldFont = (HFONT)SelectObject(hdc, g_hAppleFont);

        TEXTMETRIC tm;
        BOOL result = GetTextMetrics(hdc, &tm);

        SelectObject(hdc, oldFont);
        ReleaseDC(NULL, hdc);

        if (!result)
            return FALSE;
    }

    return TRUE;
}
