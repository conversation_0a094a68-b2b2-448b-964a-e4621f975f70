static const int8_t s_HeightMap[][129] = {
{ -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +6,  +7,  +8,  +8,  +9, +10, +11,  +9, +11, +11, +12, +12, +12, +13, +15, +15, +15, +14, +12, +10,  +9,  +9,  +9,  +8,  +8,  +7,  +7,  +7,  +7,  +7,  +5,  +5,  +5,  +4,  +4,  +3,  +3,  +3,  +4,  +4,  +4,  +4,  +5,  +6,  +7,  +8,  +8,  +9,  +9,  +9, +10, +12, +12, +12, +13, +13, +13, +13, +13, +13, +14, +16, +16, +16, +16, +17, +17, +17, +17, +17, +17, +17, +17, +16, +16, +16, +11,  +1,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +6,  +7,  +8,  +9,  +9, +10,  +9, +10, +11, +12, +12, +12, +14, +16, +16, +15, +13, +10, +10, +10, +10,  +8,  +8,  +8,  +8,  +8,  +8,  +8,  +7,  +6,  +5,  +5,  +4,  +4,  +3,  +3,  +3,  +4,  +4,  +4,  +5,  +5,  +6,  +7,  +8,  +9,  +9,  +9,  +9, +11, +12, +13, +13, +13, +13, +13, +13, +13, +13, +15, +15, +16, +16, +16, +17, +17, +17, +17, +17, +17, +17, +17, +16, +16, +16, +11,  +1,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +6,  +7,  +7,  +8,  +9, +10,  +8,  +9, +11, +12, +12, +12, +13, +14, +13, +12, +13, +11, +10, +10,  +9,  +8,  +8,  +8, +18,  +8,  +8,  +8,  +7,  +6,  +5,  +5,  +4,  +4,  +3,  +3,  +3,  +4,  +4,  +4,  +5,  +6,  +6,  +7,  +8,  +9,  +9,  +8, +10, +12, +13, +13, +13, +13, +13, +13, +13, +13, +13, +15, +14, +16, +16, +17, +17, +17, +17, +17, +17, +17, +17, +17, +16, +16, +16, +12,  +1,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +6,  +7,  +8,  +9,  +9, +10, +11, +12, +12, +12, +12, +12, +12, +12, +12, +12, +12, +11, +10, +10,  +8,  +8,  +8,  +8, +53, +53,  +7,  +7,  +6,  +5,  +4,  +4,  +4,  +3,  +3,  +3,  +4,  +4,  +4,  +5,  +6,  +6,  +7,  +8,  +9,  +9,  +9, +11, +13, +13, +13, +13, +13, +13, +13, +13, +13, +14, +14, +15, +16, +16, +17, +17, +17, +17, +17, +17, +17, +17, +17, +16, +16, +16, +12,  +2,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +6,  +7,  +8, +10, +11, +11, +11, +12, +12, +12, +12, +12, +12, +12, +12, +11, +11, +10,  +9,  +8,  +8,  +8,  +8,  +8,  +8,  +7,  +6,  +5,  +5,  +4,  +4,  +4,  +3,  +3,  +3,  +4,  +4,  +4,  +5,  +6,  +6,  +7,  +8,  +9,  +9, +10, +11, +12, +13, +13, +13, +13, +13, +13, +13, +14, +15, +15, +16, +16, +16, +17, +17, +17, +21, +17, +17, +17, +17, +16, +16, +16, +16, +12,  +4,  -2,  -5,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +6,  +6,  +7,  +8,  +9, +10, +10, +10, +10, +10, +10, +11, +10, +10, +10, +10, +10, +10, +10,  +9,  +9,  +8,  +7,  +7,  +7,  +7,  +5,  +4,  +4,  +4,  +4,  +4,  +3,  +3,  +3,  +3,  +4,  +4,  +5,  +5,  +6,  +7,  +8,  +8,  +9, +10, +11, +12, +12, +13, +13, +13, +14, +14, +14, +14, +15, +15, +16, +16, +16, +16, +16, +17, +17, +17, +17, +17, +17, +16, +16, +16, +16, +14,  +6,  -1,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6,  +7,  +7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +7,  +7,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +5,  +5,  +5,  +3,  +4,  +3,  +3,  +3,  +4,  +3,  +4,  +5,  +5,  +6,  +7,  +8,  +8, +10, +11, +12, +12, +13, +13, +13, +13, +13, +14, +14, +15, +15, +15, +15, +15, +15, +15, +16, +16, +16, +17, +17, +17, +17, +16, +16, +16, +16, +15,  +9,  +2,  -3,  -5,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +4,  +4,  +4,  +4,  +4,  +4,  +4,  +4,  +4,  +4,  +4,  +5,  +6,  +7,  +8,  +8,  +9, +10, +11, +12, +13, +13, +14, -17, -17, -17, -17, +11, +11, +12, +13, +14, +15, +16, +16, +15, +16, +16, +16, +17, +17, +17, +16, +16, +16, +16, +15, +12,  +5,  +0,  -3,  -5,  -7,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +6, -17, -17,  +6,  +7, +11, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5, -17,  +4,  +4,  +5,  +5,  +5,  +6,  +6,  +7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +12, +13, +15, +16, +16, +16, +16, +16, +16, +16, +16, +16, +16, +15, +14,  +8,  +3,  +0,  -3,  -5,  -6,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +4,  +6,  +6, -17,  +7,  +6,  +6,  +6,  +9, +11, -17, -17, -17,  +0,  +1,  +0,  +0,  +0,  -3,  -4, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +14, +16, +17, +16, +16, +16, +16, +16, +16, +15, +15, +15, +11,  +7,  +3,  +0,  -2,  -4,  -5,  -7,  -9, -11, -12, -12, -12, -11, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +5,  +5, -17,  +4,  +3,  +3,  +5,  +7,  +5, +11, -17, -17,  +0,  +0,  +1,  +0,  +0,  +0,  -1,  -3,  -3,  -3,  -5, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +16, +16, +16, +16, +16, +16, +16, +15, +15, +14, +14, +10,  +6,  +3,  +1,  -1,  -3,  -5,  -7,  -9,  -9, -11, -12, -12, -12, -12, -12, -12, -12, -11, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +4,  +5,  +4,  +5,  +5, -17,  +3,  +3,  +4,  +6,  +6,  +5,  +9, -17, -17,  +2,  +1,  +0,  +1,  +0,  +0,  +0,  -2,  -2,  -3,  -4,  -4,  -3,  +2,  +1, -17, -17, -17,  +1,  +1, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +16, +16, +16, +15, +15, +15, +15, +14, +14, +13, +13,  +9,  +6,  +4,  +2,  +0,  -2,  -5,  -6,  -8, -10, -10, -10, -11, -12, -12, -12, -12, -12, -12, -11, -11, -11, -11, -11, -11, -10,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +4,  +4,  +4,  +5,  +6, -17,  +3,  +3,  +4,  +6,  +6,  +5,  +5, -17, -17, -17,  +0,  +0,  +1,  +0,  +0,  +0,  -1,  -1,  -2,  -3,  -2,  -1,  +2,  +1,  +1,  +0,  +0,  +1,  +1, -17, -17, -17, -17, -17, -17,  +1, -17, -17, -17, -17, -17, -17, -17,  -3,  -3, -17, -17, -17, -17, -17, -17, +10,  +4,  +0,  +0, -17, -17,  +8,  +8, -17, -17, -17, -17, -17, +11, -17, -17, -17, -17, +15, +15, +14, +14, +14, +13, +13, +12, +12, +10,  +7,  +5,  +3,  +1,  -1,  -3,  -7, -10,  -9,  -8,  -9, -10, -11, -11, -12, -12, -12, -12, -11, -11, -11, -11, -11, -11, -11, -11, -11, -10, -10, -10 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +5,  +4,  +4, -17, -17,  +3,  +3,  +4, +10,  +6,  +5, -17, -17, -17, -17,  +1,  +1,  +0,  +0,  +0,  +0,  +0,  +0,  -1,  -2,  +0,  +1,  +2,  +1,  +1,  +0,  +0,  +1,  +2, -17, -17, -17, -17, -17, -17,  +1, -17, -17, -17, -17, -17, -17, -17,  -3,  -3,  +0, -17, -17, -17, -17, +10, +11,  +6,  -2,  -2,  +2,  +5, +10,  +8,  +3, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +14, +14, +13, +13, +13, +12, +12, +11, +10,  +7,  +5,  +3,  +2,  -2,  -4,  -7,  -5,  -5,  -6,  -7,  -8,  -9, -10, -10, -11, -11, -11, -11, -11, -11, -11, -11, -11, -11, -11, -10, -10, -10 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +8,  +5,  +4, -17, -17,  +3,  +3,  +3,  +5,  +5,  +3, -17, -17, -17, -17,  +1,  +0,  +1,  +2,  +1,  +1,  +1,  +3,  +3,  +0,  +2,  +2,  +2,  +1,  +1,  +0,  +0,  +1,  +2, -17, -17, -17,  +0,  +0,  +0,  +1, -17, -17, -17, -17, -17, -17, -17,  -3,  -3,  -1,  +0,  +0,  +2,  +8,  +9, +10,  +4,  -1,  -4,  +2,  +9, +10,  +8,  +5,  -2,  +1, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +14, +13, +12, +12, +11, +11, +11, +10,  +9,  +7,  +4,  +1,  -3,  -5,  -5,  -3,  -1,  -2,  -3,  -5,  -7,  -9,  -9,  -9,  -9, -10, -10, -10, -11, -11, -11, -11, -11, -11, -10, -10, -10 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +6,  +5,  +5,  +4, -17, -17, -17,  +3,  +3,  +4,  +4,  +4,  +3,  +4,  +2,  +1,  +0,  +0,  +1,  +5,  +1,  +1,  +1,  +2,  +7,  +7,  +6,  +3,  +2,  +1,  +1,  +1,  +1,  +2,  +6,  +6, -17,  +3,  +0,  +0,  +0,  +1, -17, -17, -17,  +3,  -1,  -4,  -4,  -3,  -1,  -1,  +3,  -1,  -3,  +3,  +4,  +4,  +2,  +2,  +2,  +1,  +5,  +6,  +4,  -2,  -1,  -1,  -1, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +12, +12, +11, +11, +11, +10, +10,  +8,  +5,  +1,  -2,  -5,  -4,  +0,  +2,  +2,  +1,  -2,  -5,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10, -10, -11, -11, -11, -11, -10 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +6,  +5,  +5, -17, -17, -17,  +3,  +3,  +4,  +4,  +3,  +3, +11,  +2,  +1,  +0,  +0,  +0,  +3,  +1,  +1,  +1,  +1,  +5,  +7,  +9,  +8,  +2,  +1,  +1,  +0,  +0,  +2,  +6,  +5,  +5,  +2,  +0,  -1,  +1,  +2,  +8,  +6,  +2,  +2,  +2,  +0,  -3,  -4,  -1,  -1,  -2,  -3,  -4,  -2,  -2,  -4,  -5,  -3,  -3,  -2,  +0,  +2, -17, -17,  -1,  +0,  -1,  -2, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +12, +11, +11, +10, +10,  +9,  +9,  +6,  +2,  -2,  -5,  -4,  +1,  +4,  +3,  +2,  +1,  -4,  -6,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10, -10, -10 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +3,  +4,  +5,  +5, -17, -17, -17, -17,  +8,  +4,  +3,  +4,  +3,  +3,  +2,  +0,  +0,  +0,  +0,  +2,  +2,  +1,  +1,  +1,  +3,  +6,  +9,  +9,  +3,  +1,  +1,  +0,  +1,  +2,  +3,  +5,  +3,  +1,  +0,  +0,  +4,  +7,  +7,  +3,  +2,  +0,  +0,  -1,  -2,  -4,  -1,  -2,  -3,  -4,  -6,  -7,  -8,  -9,  -9,  -8,  -9,  -7,  -3,  +3, -17, -17, -17, -17, -17, -17,  -1,  +0,  +4, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +10, +11,  +9,  +9,  +9,  +8,  +4,  -1,  -5,  -3,  +2,  +5,  +5,  +4,  +2,  +0,  -5,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +2,  +2,  +3,  +4,  +5,  +3, -17, -17, -17,  +3,  +4,  +4,  +6,  +5,  +5,  +2,  +1,  +0,  +0,  +0,  +1,  +4,  +1,  +1,  +1,  +2,  +5,  +8,  +9,  +3,  +1,  +1,  +0,  +0,  +2,  +2,  +4,  +2,  +1,  +1,  +1,  +3,  +5,  +5,  +4,  +2,  +1,  +0,  -1,  -1,  -2,  -1,  -3,  -4,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -1, -17, -17, -17, -17, -17, -17, -17, -17,  +8,  +3,  -1, -17, -17, -17, -17, -17, -17, -17, -17, -17, +10,  +9,  +8,  +9,  +8,  +5,  -1,  -5,  -2,  +5,  +7,  +6,  +5,  +4,  +2,  -2,  -5,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +1,  +1,  +2,  +3,  +4,  +3, -17, -17,  +2,  +3,  +8,  +4,  +4,  +6,  +5,  +2,  +2,  +0,  +0,  +0,  +0,  +4,  +1,  +1,  +1,  +1,  +5,  +7,  +9,  +4,  +1,  +1,  +0,  +0,  +2,  +3,  +3,  +2,  +2,  +2,  +1,  +1,  +2,  +2,  +2,  +2,  +1,  +1,  +0,  +0,  +1,  +0,  -3,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -1, -17, -17, -17, -17, -17, -17, -17,  +5,  +1,  -2,  +0, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +9,  +9,  +8,  +5,  -4,  -5,  +2,  +7,  +8,  +8,  +8,  +7,  +5,  +2,  -3,  -6,  -7, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +0,  +0,  +1,  +3,  +3,  +3, -17,  +2,  +7,  +3,  +2,  +2,  +1,  -1,  -2,  -1,  +5,  +0,  +0,  +0,  -1,  +2,  +2,  +0,  +1,  +0,  +3,  +4,  +6,  +3,  +1,  +0,  -1,  -1,  +1,  +1,  +2,  +3,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +1,  -3,  -1,  +0,  +0,  -1,  -3,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -2, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +0,  +1,  +3, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +9,  +6,  +8,  -5,  -3,  +6,  +9, +10, +10, +10, +10,  +7,  +4,  +1,  -3,  -7, -10, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +0,  +0,  +1,  +2,  +3,  +5,  +3,  +5,  +5,  +3,  +2,  +0,  -4,  -4,  -5,  -4,  -2,  -3,  -4,  -2,  +0,  +1,  +3,  +1,  +1,  -1,  +1,  +1,  +2,  -1,  -1,  -1,  -2,  -2,  -2,  -1,  -1,  +0,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +1,  -3,  -1,  -2,  -2,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4, -17, -17, -17, -17, -17, -17, -17, -17,  +1,  +3,  +1,  +0, -17, -17, -17, -17, -17, -17,  +1,  +3,  +6,  +7,  +3,  -5,  -5,  +4,  +9, +10, +10, +10, +11, +11,  +9,  +6,  +4,  +1,  -3,  -6, -11, -16, -17, -15, -10,  -9,  -9,  -9,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +0,  +0,  +0,  +1,  +2,  +3,  +4,  +3,  +7,  +5,  +3,  +1,  +1,  -3,  -7,  -9,  -9,  -6,  -6,  -6,  -4,  -1,  -4,  -2,  -1,  -1,  -2,  -1,  -2,  -2,  -3,  -3,  -3,  -4,  -5,  -4,  -4,  -3,  -2,  -1,  +0,  +1,  +2,  +2,  +1,  +0,  -2,  -4,  -4,  -4,  -6,  -6,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -5,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17, -17,  +0,  -3, -17, -17, -17,  +0,  +0,  +1,  +2,  +2,  +4,  +1,  -5,  +0,  +8,  +9, +10, +10, +10, +11, +12, +11,  +9,  +6,  +5,  +1,  -2,  -5,  -9, -14, -17, -17, -17, -16, -11,  -9,  -9,  -9 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +0,  +0,  +1,  +1,  +2,  +2,  +1,  +6, +12, +10,  +6,  +2,  +1,  -1,  -5,  -9,  -9,  -9,  -9,  -8,  -7,  -5,  -3,  -2,  -3,  -3,  -3,  -3,  -5,  -6,  -6,  -6,  -7,  -8,  -9,  -9,  -9,  -8,  -6,  -3,  -2,  -1,  -1,  -1,  -2,  -3,  -4,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17,  -1,  +2,  +9, +13, -17,  +0,  +0,  +0,  +1,  +2,  +2,  +0,  -5,  -1,  +4,  +8,  +9, +10, +11, +11, +12, +12, +12, +12,  +9,  +6,  +3,  -1,  -4,  -7, -12, -16, -17, -17, -17, -15, -14, -13 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  +0,  +1,  +2,  +3,  +1,  +3,  +4, +11, +12,  +8,  +4,  +2,  -1,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -6,  -5,  -5,  -5,  -6,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -6,  -4,  -4,  -4,  -6,  -7,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17, +14, +11, -17,  +0,  +0,  +0, -17,  +0,  +1,  -1,  -4,  -5,  +1,  +6, +10, +10, +10, +11, +12, +12, +12, +13, +14, +11,  +7,  +7,  +1,  -3,  -5,  -9, -13, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  +0,  +1,  +1,  +2,  +2,  +4,  +5,  +9, +11,  +8,  +5,  +1,  -2,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17,  +7,  +0,  +1,  +2, -17,  +0,  +1,  +0,  -1,  -3,  -5,  +0,  +7, +12, +11, -17, -17, +11, +12, +12, +13, +13, +13, +14, +13, +12, -17,  -1,  -4,  -6, -10, -14, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1,  +0,  +1,  +2,  +4,  +5,  +5,  +8,  +9, +10,  +6,  +2,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -1,  +0,  +1,  +0,  +0, -17,  +0,  +0,  +0,  +0,  +0,  -5,  -1,  +8,  +9,  +8, -17, -17, -17, -17, +12, +12, +13, +13, +13, +13, +13, -17, -17, -17,  -1,  -4,  -7, -10, -12, -14 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1,  +0,  +0,  +1,  +2,  +5,  +5,  +8,  +8,  +6,  +5,  +1,  -3,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -2,  +0,  +2,  +1,  +3,  +2,  +1,  +1,  +1,  +6,  +5,  -5,  +1,  +9,  +9,  +8, +10, +11, -17, -17, -17, +12, +12, +13, +13, +13, +13, +13, -17, -17, -17, -17,  -2,  -4,  -5,  -7 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1,  +0,  +1,  +1,  +1,  +2,  +2,  +3,  +7,  +7,  +6,  +1,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -2,  +0,  +3,  +6,  +4,  +0,  +3,  +3,  +7,  +8,  +2,  -5,  +1,  +6,  +8,  +8,  +9, +10, +11, -17, -17, -17, -17, +12, +12, +12, +12, +13, +13, -17, -17, -17, -17,  -1,  -3,  -3 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  +0,  +0,  +1,  +1,  +2,  +3,  -2,  -2,  +5,  +8,  +7,  +2,  -2,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -2,  +1,  +5,  +4,  +0,  +1,  +3,  +3,  +9,  +6,  -1,  -2,  +0,  +4,  +5,  +7,  +8, +10,  +7, -17, -17,  +5,  +6, -17, -17, +12, +13, +12, +12, +12, -17, -17, -17, -17, -17,  -1 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1,  +0,  +0,  +1,  +2,  +2,  +2,  +1,  -2,  +2,  +6,  +6,  +2,  -1,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -1,  +2,  +0,  +1,  +4,  +4,  +4,  +7,  +3,  -3,  -2,  +1,  +5, +10,  +7,  +9,  +5,  +5, -17,  +5,  +5,  +6,  +5, -17, -17, -17, +12, +12, +12, +12, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -2,  -1,  +0,  +0,  +1,  +1,  +2,  +2,  +2,  +2,  -2,  +1,  +6,  +6,  +3,  -2,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -3,  +0,  +1,  +4,  +2,  +2,  +2,  +1,  -5,  -1,  +4,  +5,  +5,  +8,  +5,  +5,  +4,  +5,  +5,  +6,  +5,  +4,  +4, -17, -17, -17, +11, +11, +12, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17,  -3,  -3,  -3,  -2,  +0,  +1,  +2,  +2,  +2,  +2,  +2,  +3,  +3,  +6,  +8,  +4,  +0,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -5,  -5,  -4,  -3,  -3,  -2,  +0,  -1,  -2,  -3,  -5,  -5,  -1,  +3,  +4,  +4,  +4,  +5,  +4,  +6,  +5,  +6,  +5,  +4,  +4, -17, -17, -17, -17, -17, +11, +11, +11, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17,  -4,  -5,  -5,  -3,  -2,  +0,  +1,  +1,  +1,  +2,  +4,  +6,  +6,  +8,  +8,  +3,  +0,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -8,  -7,  -6,  -5,  -5,  -4,  -5,  -5,  -5,  -4,  -4,  -3,  -2,  +1,  +3,  +4,  +3,  +4,  +6,  +5,  +5,  +5,  +4,  +4,  +5, -17, -17, -17, -17, -17, -17, +10, +11, +11, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17,  -4,  -4,  -3,  -5,  -6,  -6,  -6,  -4,  -3,  -1,  +0,  +0,  +2,  +4,  +6,  +6,  +5,  +5,  +5,  +1,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10,  -9,  -7,  -8, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -8,  -5,  -4,  -4,  -5,  -5,  -4,  -4,  -4,  -4,  -3,  -3,  -3,  -1,  +2,  +4,  +4,  +5,  +4,  +5,  +5,  +4,  +4,  +5,  +5, -17, -17, -17, -17, -17, -17, +10, +10, +11, -17, -17, -17 },
{ -17, -17, -17, -17, -17,  -3,  -2,  +0,  -1,  -2,  -2,  -3,  -3,  -3,  -3,  -2,  -1,  +0,  +2,  +3,  +4,  +6,  +8,  +7,  +4,  +1,  -3,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10,  -8,  -5,  -4,  -5,  -7, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3, -17, -17,  +5,  +6,  +5,  +5,  +4,  +6,  +5,  +4,  +4,  +4, -17, -17, -17, -17, -17, -17,  +9, +10, -17, -17, -17 },
{ -17, -17, -17, -17, -17,  -1,  +0,  +2,  +4,  +4,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +1,  +2,  +5,  +6,  +5,  +6,  +4,  +4,  +0,  -3,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10,  -7,  -5,  -1,  -2,  -4,  -8, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17,  +5,  +6,  +5,  +5,  +5,  +5,  +9,  +4,  +4,  +4,  +4,  +4, -17, -17, -17, -17,  +9, +10, -17, -17, -17 },
{ -17, -17, -17,  -1,  +3,  +7, +10,  +9,  +7,  +7,  +8,  +8,  +8,  +8, +12, +16, +17, +14, +11, +10,  +5,  +3,  +3,  +3,  +3,  +1,  -3,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10,  -9,  -5,  -2,  -1,  -3,  -6, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17,  +6, -17, -17,  +5,  +5,  +4,  +5,  +5,  +4,  +4,  +4,  +4,  +4, -17, -17, -17,  +9, +10, +10, -17, -17 },
{ -17, -17,  +1,  +5,  +9, +12, +16, +20, +22, +21, +19, +17, +15, +17, +23, +29, +25, +18, +12,  +7,  +3,  +1,  +0,  +1,  +1,  -1,  -3,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10,  -7,  -5,  -5,  -5,  -7, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3, -17, -17, -17, -17, -17,  +5,  +5,  +5,  +5,  +4,  +4,  +4,  +4,  +4,  +4, -17,  +9,  +9, +10, -17, -17 },
{ -17,  +2,  +7, +11, +16, +21, +25, +28, +31, +33, +34, +30, +26, +25, +28, +31, +22, +13,  +8,  +4,  +1,  +0,  +0,  -1,  +0,  -1,  -2,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10, -10, -10,  -9,  -9, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17, -17, -17, -17,  +4,  +4,  +6,  +5,  +4,  +4,  +5,  +4, -17, -17,  +9,  +9, +10, -17, -17 },
{  +2,  +7, +11, +16, +21, +27, +32, +37, +40, +43, +42, +39, +35, +32, +33, +29, +18, +10,  +7,  +4,  +1,  +0,  +0,  +0,  +0,  +0,  -2,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9, -10,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3, -17, -17, -17, -17, -17, -17,  +7,  +5,  +4,  +4,  +6,  +4, -17, -17,  +9,  +9,  +9, -17, -17 },
{  +7, +11, +15, +20, +24, +29, +35, +41, +46, +52, +51, +47, +43, +38, +37, +33, +27, +17,  +9,  +5,  +2,  +1,  +2,  +2,  +2,  +1,  +0,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17,  +9,  +7,  +5,  +5,  +6, -17, -17, -17, -17,  +9,  +9, -17, -17 },
{ +11, +16, +20, +24, +29, +33, +37, +43, +49, +54, +56, +54, +51, +46, +39, +32, +25, +20, +13,  +9,  +5,  +4,  +4,  +4,  +4,  +1,  -1,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17,  +9,  +5,  +6,  +5, -17, -17, -17, -17,  +9,  +9, -17, -17 },
{ +14, +18, +22, +27, +32, +37, +42, +46, +51, +56, +57, +59, +56, +51, +45, +37, +28, +20, +15, +11,  +7,  +7,  +5,  +4,  +2,  -1,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17,  +5,  +4,  +4,  +4, -17, -17, -17,  +9,  +9,  +9, -17, -17 },
{ +17, +21, +25, +29, +34, +39, +44, +49, +54, +57, +59, +61, +61, +56, +49, +42, +32, +22, +17, +14, +10, +10,  +7,  +3,  +1,  -3,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17, -17,  +3,  +3,  +3,  +4, -17, -17, -17,  +8,  +8,  +8, -17, -17 },
{ +19, +24, +28, +32, +36, +40, +45, +50, +55, +58, +60, +62, +62, +59, +51, +43, +33, +24, +19, +15, +12, +11,  +7,  +3,  +0,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17,  +3,  +4,  +3,  +3,  +3, -17, -17, -17,  +8,  +8,  +8, -17, -17 },
{ +19, +23, +28, +33, +38, +43, +47, +52, +57, +59, +59, +58, +56, +53, +48, +41, +32, +23, +18, +15, +11, +10, +10,  +3,  +0,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -5,  -4,  -5,  -8,  -9,  -7,  -5,  -4,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4, -17, -17,  +6,  +5, -17,  +3,  +3, -17, -17, -17, -17,  +7,  +8, -17, -17 },
{ +22, +25, +29, +32, +37, +42, +47, +52, +56, +54, +53, +52, +49, +46, +41, +35, +27, +20, +15, +12,  +8, +10,  +9,  +4,  +0,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -3,  -3,  -4,  -4,  -4,  -4,  -3,  -3,  -3,  -4,  -6,  -8,  -7,  -7,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -1, -17,  +6,  +5, -17, -17, -17, -17, -17, -17, -17,  +7,  +8, -17, -17 },
{ +31, +31, +32, +34, +38, +42, +47, +52, +54, +54, +53, +50, +48, +41, +31, +27, +23, +17, +14, +11,  +7,  +8,  +6,  +3,  -1,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -5,  -4,  -3,  -3,  -1,  -1,  -3,  -4,  -4,  -3,  -3,  -2,  -2,  -3,  -3,  -4,  -4,  -4,  -4,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -1, -17, -17,  +2, -17, -17, -17, -17, -17, -17,  +7,  +7,  +7, -17, -17 },
{ +38, +43, +46, +47, +47, +48, +49, +51, +52, +52, +52, +51, +46, +33, +23, +17, +14, +13, +11,  +9,  +7,  +8,  +5,  +2,  -1,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -3,  -3,  -2,  +0,  +0,  +0,  -4,  -4,  -3,  -2,  -1,  -2,  -2,  +0,  -2,  -2,  -1,  -3,  -3,  -5,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -1, -17, -17,  -1,  -1, -17, -17, -17,  -1,  -1, -17,  +6,  +6, -17, -17 },
{ +40, +46, +51, +56, +61, +62, +63, +63, +57, +54, +49, +44, +38, +21,  +9,  +4,  +5,  +9, +11,  +9,  +6,  +5,  +5,  +2,  -3,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -3,  -3,  -3,  -2,  -1,  +3,  -1,  -2,  -3,  -4,  +0,  +0,  -1,  -2,  -2,  -1,  -2,  -1,  -1,  -2,  -3,  -4,  -6,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -1,  -1,  -2,  -2,  -2,  -2,  -1,  -1,  -1, -17, -17,  +6,  +7, -17, -17 },
{ +42, +47, +53, +58, +64, +69, +75, +75, +69, +61, +51, +47, +38, +20,  +3,  +2,  +3,  +7, +10,  +8,  +3,  +3,  +4,  +1,  -3,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -3,  -3,  -3,  -4,  -3,  -2,  -2,  +0,  -1,  -3,  -2,  +1,  +0,  +0,  -1,  -3,  -2,  -3,  -3,  -2,  -1,  -3,  -3,  -3,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -2,  -2,  -2,  -2,  +0,  -1, -17, -17, -17, -17,  +5,  +6, -17, -17 },
{ +43, +49, +54, +60, +66, +71, +77, +78, +74, +72, +66, +56, +48, +30, +14,  +3,  +3,  +7,  +9,  +6,  +4,  +4,  +3,  -1,  -2,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -3,  -3,  -3,  -3,  -2,  -1,  +3,  +1,  -1,  +2,  +2,  +2,  +1,  +0,  -1,  -1,  -3,  -3,  -2,  -2,  -3,  -4,  -3,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -2,  -1,  -1,  -1,  -1, -17, -17, -17, -17,  +5,  +5,  +5, -17, -17 },
{ +44, +50, +56, +61, +67, +73, +78, +78, +74, +70, +70, +66, +65, +47, +25,  +7,  +6,  +7,  +8,  +5,  +4,  +3,  +1,  -1,  -3,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -8,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -4,  -4,  -3,  -3,  -3,  -2,  -1,  -2,  -1,  +0,  +3,  +1,  +1,  +0,  +0,  -1,  -2,  -2,  -2,  -3,  -3,  -4,  -5,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -2,  +0,  +0,  +0,  -1,  -1, -17, -17, -17, -17, -17, -17,  +4,  +5, -17, -17 },
{ +43, +50, +56, +62, +68, +74, +80, +78, +75, +72, +71, +70, +63, +48, +29, +15,  +9,  +7,  +7,  +6,  +3,  +2,  +0,  -2,  -3,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -6,  -4,  -3,  -4,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -3,  -3,  -3,  -2,  -2,  -3,  -3,  -2,  +0,  -1,  -1,  +1,  +2,  -3,  -2,  -2,  -2,  -1,  -3,  -4,  -3,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -1,  +1,  -1,  +1,  +5,  +5, -17, -17, -17, -17, -17, -17,  +4,  +5, -17, -17 },
{ +42, +48, +55, +61, +68, +74, +80, +79, +77, +74, +73, +70, +61, +48, +35, +21, +10,  +7,  +7,  +4,  +1,  +0,  +1,  +0,  -2,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -3,  -3,  -3,  -3,  -3,  -3,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -2,  -2,  -2,  -2,  -3,  -1,  -1,  -3,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -1,  -1,  +3,  +6,  +7,  +7, -17, -17, -17, -17, -17, -17,  +3,  +4, -17, -17 },
{ +41, +47, +54, +60, +66, +73, +77, +79, +79, +78, +76, +73, +63, +48, +33, +23, +14,  +7,  +7,  +4,  +0,  +6, +10,  +8,  -2,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -6, -17, -17, -17,  -3,  -3,  -3,  -3,  -3,  -3,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -4,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -2,  -2,  -1,  +2,  +0,  -1,  -3,  -5,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -2,  -1,  +0,  +5,  +7, +11,  +7, -17, -17, -17, -17, -17, -17,  +3,  +4, -17, -17 },
{ +36, +43, +51, +59, +65, +71, +74, +76, +78, +78, +78, +73, +64, +50, +34, +22, +11,  +8,  +7,  +3,  +0,  +7, +17, +21, +10,  -4,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -5,  -3, -17, -17, -17, -17,  -1,  -2,  -3,  -3,  -3,  -2,  -2,  -4,  -4,  -6,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -3,  -2,  -2,  -1,  +2,  +1,  -2,  -2,  -3,  -4,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -2,  -1,  -2,  -2,  +0,  +6,  +7,  +9,  +6,  +6, -17, -17, -17, -17, -17,  +3,  +3, -17, -17 },
{ +30, +36, +44, +52, +60, +67, +71, +73, +75, +76, +78, +74, +63, +49, +36, +22, +11,  +7,  +6,  +3,  +1,  +7, +18, +26, +20,  +5,  -2,  -4,  -7,  -8,  -9,  -9,  -9,  -9,  -9,  -6,  -4, -17, -17, -17, -17,  -1,  +1,  +0,  -1,  -1,  -2,  -1,  +0,  +3,  -2,  -3,  -4,  -4,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -6,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -3,  -2,  -2,  +0,  +1,  -2,  -2,  -3,  -3,  -5,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -2,  -2,  -2,  -3,  -4,  -3,  +1,  +4,  +6,  +5, -17, -17, -17, -17, -17, -17, -17,  +2,  +3, -17, -17 },
{ +28, +34, +39, +45, +53, +60, +63, +66, +69, +72, +75, +73, +62, +47, +33, +21,  +9,  +6,  +5,  +2,  +0,  +6, +17, +23, +24, +16,  +5,  +2,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -2, -17, -17, -17, -17, -17,  +2,  +3,  +2,  +2,  +1,  +0,  +0,  +0,  +5,  +2,  -1,  -3,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -3,  -2,  -2,  -1,  +0,  -2,  -3,  -3,  -4,  -3,  -5,  -8,  -9,  -9,  -8,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -1,  -2,  -3,  -3,  -4,  -4,  +0,  +4,  +6,  +4,  +4, -17, -17, -17, -17, -17, -17,  +2,  +2, -17, -17 },
{ +25, +30, +36, +42, +47, +52, +55, +59, +62, +66, +70, +69, +60, +44, +31, +20,  +9,  +6,  +4,  +1,  -1,  +6, +19, +17, +17, +21, +15, +10,  +2,  -1,  -2,  -2,  -2,  -2,  -1, -17, -17, -17, -17, -17, -17,  +1,  +1,  +3,  +1,  +1,  +1,  +1,  +0,  +0,  +6,  +2,  +0,  -3,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -5,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -2,  -3,  -5,  -5,  -2,  -1,  -3,  -4,  -6,  -8,  -5,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -2,  -3,  -3,  -3,  -3,  -4,  -4,  +0,  +4,  +6,  +6, -17, -17, -17, -17, -17, -17, -17,  +1,  +2, -17, -17 },
{ +22, +27, +32, +38, +44, +47, +50, +53, +56, +60, +65, +65, +59, +45, +30, +19, +10,  +6,  +3,  -1,  -1,  +6, +19,  +2,  +2,  +0,  +8, +16,  +7,  +3,  +1,  +0,  +0,  +0,  +1, -17, -17, -17, -17, -17,  +4,  +3,  +3,  +5,  +1,  +2,  +1,  +2,  -1,  -3,  +5,  +4,  +1,  -2,  -3,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -4,  -3,  -3,  -3,  -4,  -4,  -5,  -5,  -5,  -3,  -1,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -2,  -1,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  +1,  +5,  +5,  +5,  +6, -17, -17, -17, -17, -17, -17,  +1,  +1, -17, -17 },
{ +21, +25, +29, +35, +40, +43, +45, +48, +51, +54, +58, +57, +53, +44, +33, +22, +12,  +6,  +4,  -1,  -1,  +4, +17,  +6,  +1,  +1,  +3, +18,  +8,  +4,  +3,  +2,  +2,  +1,  +1, -17, -17, -17, -17, -17,  +2,  +5,  +4,  +5,  +5,  +0,  +1,  +1,  -1,  -3,  +2,  +4,  +1,  -2,  -3,  -4,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -3,  -3,  -4,  -4,  -5,  -5,  -5,  -4,  -3,  -4,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -1,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  +2,  +5,  +5,  +5,  +5, -17, -17, -17, -17, -17, -17,  +0,  +1, -17, -17 },
{ +19, +24, +28, +32, +36, +38, +40, +43, +45, +48, +51, +52, +46, +40, +33, +24, +14,  +8,  +3,  -1,  +0,  +4,  +9, +16,  +3,  +0,  +1, +15,  +7,  +4,  +3,  +2,  +2,  +1,  +1, -17, -17, -17, -17,  +3,  +5,  +9,  +8,  +8, +11,  +4,  +2,  +2,  +1,  -3,  -1,  +4,  +1,  -3,  -3,  -4,  -4,  -4,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -4,  -4,  -3,  -3,  -4,  -4,  -4,  -5,  -4,  -4,  -3,  -4,  -3,  -4,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -1,  -2,  -3,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  +2,  +5,  +6, -17, -17, -17, -17, -17, -17, -17,  +0,  +1,  +1, -17, -17 },
{ +18, +22, +27, +31, +35, +36, +34, +36, +39, +42, +43, +44, +37, +31, +26, +19, +12,  +7,  +4,  +0,  +0,  +7,  +8, +13, +14,  +1,  +4, +12,  +3,  +1,  +2,  +0,  +2,  +1, -17, -17, -17, -17,  +1,  +3,  +7, +12, +12, +13, +12, +12,  +7,  +2,  +2,  +5,  -1,  +3,  +0,  -2,  -3,  -3,  -4,  -4,  -4,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -6,  -4,  -4,  -3,  -3,  -4,  -4,  -4,  -5,  -6,  -3,  -4,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  -2,  -1,  -2,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -2,  +5,  +6,  +7, -17, -17, -17, -17, -17, -17, -17,  +0,  +0,  +0, -17, -17 },
{ +16, +21, +25, +30, +33, +32, +30, +29, +32, +36, +36, +36, +32, +27, +23, +16, +10,  +6,  +3,  +0,  +0,  +5,  +8,  +9, +11,  +2,  +6,  +5,  +1,  -1,  +0,  +0,  +2,  +1, -17, -17,  +4,  +2,  +3,  +7, +10, +15, +15, +16, +16, +14, +11,  +6,  +3,  +2,  +1,  +1,  -2,  -2,  -2,  -3,  -3,  -3,  -3,  -4,  -4,  -6,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -4,  -3,  -3,  -4,  -4,  -6,  -6,  -4,  -3,  -4,  -3,  -4,  -3,  -4,  -4,  -4,  -4,  -4,  -3,  -2,  -2,  -2,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  +1,  +6,  +7,  +7, -17, -17, -17, -17, -17, -17, -17,  -1,  +0,  +0, -17, -17 },
{ +15, +19, +24, +28, +31, +29, +28, +26, +25, +27, +31, +33, +31, +27, +19, +12,  +9,  +6,  +2,  +1,  +0,  +5, +11, +10, +10,  +4,  +4,  +1,  +0,  -1,  +5,  +1,  +1, -17,  +3,  +6,  +5,  +4,  +9, +14, +12, +16, +17, +16, +17, +18, +13, +12,  +5,  +3,  -1,  -1,  -2,  +0,  -2,  -2,  -2,  -3,  -3,  -3,  -4,  -4,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -5,  -4,  -4,  -3,  -3,  -3,  -5,  -5,  -5,  -5,  -4,  -3,  -1,  -1,  -2,  -4,  -4,  -4,  -4,  -3,  -2,  -1,  -2,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  +3,  +7,  +8,  +6, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1, -17, -17 },
{  +9, +14, +20, +25, +28, +26, +24, +22, +20, +19, +21, +22, +23, +23, +18, +11,  +8,  +6,  +3,  +1,  +3,  +5, +13, +11,  +9,  +5,  +1,  +0,  -1,  -1,  +0,  +1,  +1,  +1,  +1,  +5,  +8,  +9, +10, +10, +13, +15, +15, +13, +12, +15, +16, +15, +10,  +6,  +5,  +2,  +0,  -4,  -3,  -1,  -1,  -5,  -4,  -3,  -4,  -4,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -3,  -4,  -4,  -5,  -5,  -6,  -6,  -4,  -1,  +1,  -2,  -3,  -4,  -4,  -3,  -2,  -2,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -2,  +3,  +6,  +6,  +8, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1, -17, -17 },
{  +4,  +8, +14, +19, +23, +22, +19, +17, +15, +13, +13, +15, +17, +18, +15, +11,  +8,  +5,  +2,  +1,  +3,  +7, +10, +10,  +9,  +6,  +0,  -1,  -2,  -1,  -1,  +1,  +1,  +0,  +1,  +6, +11, +10,  +8,  +8, +12, +13, +13, +12, +12, +18, +13, +13, +12,  +5,  +5,  +2,  +2,  +1,  -2,  -1,  -1,  -5,  -5,  -3,  -3,  -4,  -2,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -4,  -3,  -4,  -6,  -7,  -7,  -7,  -6,  -7,  -2,  +0,  +2,  -2,  -3,  -2,  -1,  -2,  -3,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -2,  +3,  +4,  +2, -17, -17, -17, -17, -17, -17, -17, -17,  -2,  -1,  -1, -17, -17 },
{  +3,  +5,  +8, +13, +18, +16, +15, +13, +11,  +9,  +9, +16, +19, +15, +13, +10,  +8,  +5,  +2,  +1,  +3,  +5,  +7,  +8,  +8,  +6,  +4,  +1,  -1,  -1,  -1,  +4,  +5,  +2,  +1,  +5,  +8,  +8,  +6,  +5,  +8, +11, +10, +12, +13, +11,  +9, +12, +11, +11, +10,  +4,  +1,  +2,  +3,  -2,  -2,  -4,  -4,  -2,  -3,  -3,  -4,  -3,  -8,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -3,  -4,  -5,  -7,  -8,  -8,  -7,  -7,  -8,  -3,  +1,  +4,  +2,  -1,  -1,  -1,  -1,  -1,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  +0,  +3,  +2,  +0, -17, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1, -17, -17 },
{  -3,  +2,  +6,  +9, +12, +11, +10,  +8,  +7,  +4,  +4, +10, +22, +18, +14, +10,  +7,  +4,  +2,  +1,  +3,  +4,  +6,  +7,  +7,  +5,  +4,  +2,  +0,  +0,  +0,  +7,  +7,  +4,  +1,  +3,  +5,  +5,  +4,  +4,  +4, +11,  +8,  +5,  +6,  +7,  +8,  +8, +12,  +7,  +6,  +1,  -2,  +1,  +3,  +1,  -2,  -2,  +0,  +3,  +0,  -3,  -4,  -4,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -3,  -4,  -5,  -6,  -7,  -8,  -8,  -8,  -6,  -4,  +0,  +1,  -1,  +2,  +3,  +3,  +1,  +1,  +0,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -3,  +0,  -1,  -3,  -1,  -1, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1, -17, -17 },
{  -5,  -5,  -4,  +0,  +5,  +7,  +5,  +3,  +2,  +0,  -1,  +6, +13, +18, +14, +10,  +7,  +4,  +2,  +1,  +2,  +3,  +4,  +5,  +5,  +5,  +4,  +2,  +1,  +0,  +2,  +7,  +9,  +8,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +4,  +3,  +3,  +4,  +7,  +6,  +6,  +6,  +4,  -4,  -8,  -4,  +2,  +2,  +0,  -2,  +1,  +5,  +7,  +0,  -3,  -4,  -4,  -5,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -4,  -4,  -3,  -3,  -3,  -3,  -4,  -5,  -7,  -8,  -6,  -4,  -3,  -2,  -1,  +1,  +2,  +4,  +4,  +4,  +3,  +2,  +0,  -1,  -2,  -3,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -4,  -3,  -1,  +2,  -2,  -4,  -3,  -3, -17, -17, -17, -17, -17, -17,  -2,  -1,  -1, -17, -17 },
{ -17, -17, -17,  -6,  -5,  -1,  +0,  +1,  -1,  -3,  -6,  -3,  +1,  +6,  +8,  +8,  +6,  +4,  +2,  +0,  +1,  +2,  +3,  +4,  +3,  +4,  +3,  +2,  +1,  +1,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +1,  -3,  -9, -13,  -5,  +3,  +2,  +1,  +2,  +5,  +3,  +5,  -1,  -3,  -4,  -4,  -4,  -7,  -9,  -9,  -9,  -9,  -9,  -9,  -9,  -8,  -5,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -4,  -6,  -6,  -5,  -3,  -2,  -1,  +6,  +5,  +2,  +3,  +4,  +4,  +4,  +3,  +2,  +1,  +0,  -1,  -2,  -3,  -3,  -3,  -3,  -4,  -4,  -3,  -3,  -2,  -1,  -2,  -3,  -3,  -2, -17, -17, -17, -17, -17, -17,  -1,  -1,  -1, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17,  -7,  -5,  -5,  -7,  -7,  -8,  -5,  -3,  -1,  +2,  +2,  +1,  +1,  +1,  +2,  +3,  +3,  +3,  +3,  +3,  +2,  +2,  +2,  +3,  +4,  +3,  +3,  +3,  +3,  +3,  +2,  +1,  +1,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +0,  -3,  -8, -12, -13,  -3,  +2,  +2,  +0,  +0,  +1,  +3,  +4,  -2,  -3,  -4,  -4,  -4,  -5,  -8,  -9,  -9,  -9,  -9,  -7,  -4,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -5,  -5,  -4,  -1,  -2,  -1,  +6,  +6,  +3,  +3,  +4,  +4,  +4,  +4,  +4,  +3,  +2,  +2,  +1,  +1,  +0,  +0,  -1,  -2,  -3,  -2,  -3,  -1,  +0,  +0,  -3,  -3, -17, -17, -17, -17, -17, -17, -17,  +0,  +0,  -1, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -9,  -8,  -9,  -5,  -3,  -2,  +0,  +0,  +0,  +1,  +2,  +2,  +3,  +3,  +3,  +2,  +2,  +2,  +2,  +2,  +4,  +4,  +3,  +3,  +3,  +3,  +2,  +0,  +0,  +0,  +1,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +1,  +0,  -2,  -5,  -9,  -8,  +2,  +2,  +0,  +5,  +2,  +1,  +7,  +4,  -2,  -3,  -3,  -3,  -4,  -4,  -4,  -6,  -5,  -4,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -4,  -2,  -3,  -3,  -3,  +0,  +4,  +5,  +4,  +2,  +4,  +4,  +4,  +4,  +4,  +4,  +5,  +5,  +4,  +3,  +2,  +1,  +1,  -1,  -1,  -1,  +0,  +2,  +3,  +2,  +0,  -3, -17, -17, -17, -17, -17, -17, -17,  +1,  +0, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1,  +0,  +1,  +1,  +0,  +1,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +1,  +1,  +1,  +1,  +2,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +1,  +3,  +2,  +2,  +1,  +4,  +1,  +1,  +2,  +6,  +1,  -3,  -3,  -3,  -3,  -3,  -4,  -4,  -4,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -1,  -3,  -3,  -3,  +1,  +2,  +3,  +4,  +5,  +3,  +3,  +4,  +4,  +4,  +4,  +3,  +4,  +5,  +3,  +3,  +2,  +2,  +2,  +0,  +2,  +2,  +3,  +5,  +4,  +6,  +2,  -1, -17, -17, -17, -17, -17, -17,  +1,  +1,  +1, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +1,  +1,  +2,  +2,  +3,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +3,  +2,  +2,  +2,  +3,  +2,  +2,  +2,  +2,  +2,  +3,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +2,  +2,  -2,  -2,  +2,  +2,  +1,  -1,  -2,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -2,  -3,  -2,  -3,  +0,  +2,  +3,  +5,  +7,  +8,  +7,  +8,  +5,  +4,  +4,  +4,  +5,  +4,  +5,  +5,  +3,  +4,  +2,  +3,  +4,  +5,  +4,  +5,  +5,  +5,  +5,  +5,  +6,  +3, -17, -17, -17, -17, -17, -17,  +3,  +2,  +2, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +2,  +2,  +3,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +4,  +3,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +2,  +3,  +3,  +1,  +0,  +2,  +1,  -1,  -2,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -1,  -3,  -3,  -3,  -1,  +3,  +5, +11, +10,  +8,  +8,  +8,  +7,  +5,  +4,  +5,  +4,  +4,  +5,  +3,  +6,  +4,  +5,  +2,  +2,  +3,  +4,  +5,  +4,  +4,  +5,  +4,  +6,  +4, -17, -17, -17, -17, -17, -17,  +3,  +3,  +3, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +4,  +4,  +4,  +4,  +3,  +3,  +2,  +2,  +2,  +3,  +2,  +3,  +4,  +4,  +4,  +2,  +3,  +4,  +4,  +3,  +3,  +3,  +2,  +3,  +2,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +3,  +2,  +0,  +1,  +0,  -1,  -2,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -2,  -2,  -3,  -3,  -2,  +1,  +2,  +2, +10,  +8,  +7,  +8,  +7,  +8,  +9,  +6,  +5,  +6,  +6,  +5,  +6,  +4,  +5,  +4,  +3,  +4,  +2,  +1,  +3,  +4,  +5,  +4,  +4,  +1,  +1,  -1,  -2, -17, -17, -17,  +1,  +2,  +2,  +3, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +6,  +5,  +5,  +5,  +4,  +3,  +3,  +3,  +3,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +4,  +3,  +4,  +5,  +3,  +4,  +4,  +3,  +3,  +3,  +3,  +3,  +2,  +3,  +2,  +3,  +2,  +3,  +3,  +2,  +2,  +2,  +3,  +3,  +2,  +2,  +2,  +2,  +2,  +1,  -1,  -2,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -3,  -2,  -1,  -1,  -2,  -2,  +0,  +2,  +1,  +2,  +2,  +4, +10,  +9,  +8,  +8,  +8,  +9,  +9,  +7,  +7,  +7,  +6,  +7,  +6,  +6,  +5,  +4,  +4,  +2,  +2,  +1,  -2,  -4,  -5,  -6,  -6,  -3,  -6,  -5,  -4,  -2,  -1,  +1,  +2,  +3, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +6,  +7,  +7,  +7,  +7,  +6,  +5,  +5,  +4,  +4,  +4,  +3,  +4,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +4,  +4,  +3,  +4,  +4,  +3,  +5,  +4,  +4,  +4,  +3,  +4,  +5,  +2,  +2,  +3,  +2,  +2,  +2,  +3,  +2,  +2,  +2,  +0,  -3,  +1,  +1,  +0,  -1,  -2,  -3,  -3,  -3,  -3,  -3,  -3,  -2,  -2,  -2,  -2,  -1,  -1,  +1,  +0,  -1,  +0,  +2,  +4,  +2,  +0,  +0,  +2,  +5, +10, +10,  +9,  +9,  +9, +10,  +9,  +9,  +8,  +7,  +7,  +6,  +6,  +5,  +4,  +2,  +0,  -1,  -3,  -5,  -6,  -7,  -8,  -7,  -7,  -7,  -6,  -4,  -3,  -1,  +1,  +1, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +8,  +7,  +7,  +7,  +7,  +6,  +6,  +5,  +5,  +5,  +5,  +5,  +5,  +5,  +4,  +4,  +3,  +3,  +2,  +3,  +3,  +3,  +3,  +4,  +3,  +4,  +4,  +4,  +4,  +2,  +4,  +4,  +2,  +3,  +4,  +4,  +4,  +4,  +3,  +2,  +2,  +0,  -3,  -3,  +0,  -1,  -2,  -2,  -3,  -3,  -3,  -3,  -4,  -3,  -1,  +0,  -1,  -1,  -1,  -2,  +1,  +1,  +1,  +1,  +1,  +2,  +3,  +4,  +2,  +0,  +2,  +4,  +6,  +9, +12, +11, +11, +11, +11,  +9,  +8,  +7,  +7,  +6,  +6,  +5,  +4,  +2,  +0,  -1,  -2,  -5,  -5,  -7,  -8,  -7,  -7,  -7,  -7,  -6,  -4,  -2,  +0, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +8,  +7,  +7,  +7,  +7,  +6,  +6,  +6,  +5,  +6,  +7,  +7,  +6,  +6,  +5,  +6,  +6,  +6,  +5,  +4,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +2,  +3,  +4,  +4,  +4,  +4,  +4,  +3,  +2,  -1,  -3,  -3,  -1,  -3,  -5,  -3,  -3,  -3,  -3,  -2,  -3,  -2,  +0,  +1,  +0,  -1,  +0,  +1,  +1,  +1,  +2,  +2,  +1,  +2,  +2,  +4,  +5,  +4,  +2,  +4,  +6,  +8, +10, +12, +12, +11, +10, +10,  +9,  +8,  +8,  +7,  +7,  +6,  +5,  +3,  +2,  -1,  -2,  -4,  -5,  -7,  -7,  -7,  -7,  -7,  -7,  -7,  -5,  -3, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +8,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +6,  +8,  +9,  +9,  +8,  +8,  +8,  +8,  +8,  +7,  +7,  +6,  +5,  +4,  +3,  +3,  +2,  +3,  +3,  +3,  +3,  +3,  +2,  +3,  +4,  +4,  +4,  +4,  +4,  +3,  +2,  -1,  -3,  -2,  -2,  +0,  -5,  -3,  -3,  -3,  -3,  -1,  -2,  -1,  +0,  -1,  +0,  +0,  +0,  +1,  +1,  +1,  +2,  +2,  +1,  +2,  +2,  +4,  +6,  +7,  +7,  +4,  +6,  +9, +12, +12, +12, +12, +11, +10, +10,  +8,  +8,  +7,  +7,  +6,  +5,  +4,  +2,  +1,  -2,  -3,  -4,  -6,  -7,  -8,  -7,  -7,  -7,  -7,  -7,  -5, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +7,  +7,  +7,  +7,  +7,  +7,  +8,  +9,  +9,  +9,  +8,  +9, +10, +10,  +9,  +9,  +8,  +6,  +5,  +4,  +2,  +2,  +2,  +2,  +2,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +3,  +1,  -2,  -3,  -2,  -4,  -4,  -2,  -4,  -4,  -4,  -3,  -1,  -1,  -1,  -1,  +1,  +1,  +1,  +1,  +1,  +1,  +1,  +1,  +2,  +2,  +2,  +4,  +6,  +7,  +8,  +8,  +9,  +8, +10, +11, +11, +12, +12, +12, +11, +10,  +9,  +8,  +7,  +7,  +6,  +6,  +4,  +3,  +2,  -1,  -2,  -4,  -5,  -6,  -7,  -7,  -7,  -7,  -7,  -7,  -6, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +7,  +8,  +9,  +9, +10,  +9,  +9,  +9, +10, +10, +11, +10,  +9,  +8,  +6,  +5,  +4,  +2,  +2,  +1,  +1,  +1,  +2,  +2,  +2,  +3,  +2,  +2,  +3,  +3,  +3,  +3,  +1,  -2,  -3,  +0,  -3,  -4,  -5,  -5,  -5,  -3,  -1,  +0,  +0,  -1,  +0,  +2,  +2,  +2,  +2,  +1,  +1,  +2,  +3,  +3,  +4,  +3,  +6,  +8,  +9, +10, +10,  +9,  +8,  +8, +10, +12, +13, +13, +12, +11, +10,  +9,  +8,  +8,  +7,  +7,  +6,  +5,  +4,  +2,  +0,  -1,  -3,  -4,  -6,  -7,  -7,  -7,  -7,  -7,  -7,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +9, +10, +10, +11, +11, +10,  +9,  +8,  +6,  +5,  +4,  +2,  +2,  +1,  +1,  +1,  +2,  +1,  +1,  +2,  +3,  +3,  +2,  +2,  +2,  +2,  +1,  -3,  -3,  +2,  +0,  -1,  -3,  -3,  -2,  -1,  -1,  -1,  +1,  +1,  +1,  +3,  +2,  +2,  +2,  +2,  +3,  +2,  +3,  +4,  +5,  +6,  +6, +11, +11, +11, +10,  +9,  +9,  +9, +10, +12, +13, +13, +12, +11, +10,  +9,  +8,  +8,  +7,  +7,  +6,  +6,  +4,  +3,  +2,  -1,  -2,  -3,  -5,  -6,  -7,  -7,  -7,  -7,  -7,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +10, +11, +11, +11, +10,  +9,  +8,  +6,  +5,  +4,  +2,  +1,  +1,  +1,  +1,  +1,  +1,  +3,  +3,  +3,  +2,  +2,  +1,  +1,  +1,  +0,  -2,  -3,  +3,  +4,  +4,  +5,  +0,  +2,  +6,  +3,  +4,  +3,  +2,  +1,  +2,  +1,  +1,  +2,  +3,  +2,  +3,  +3,  +4,  +6,  +7,  +5, +11, +11, +10, +11,  +9,  +9,  +9, +10, +12, +13, +13, +12, +11, +10,  +9,  +9,  +8,  +7,  +7,  +7,  +6,  +5,  +4,  +2,  +0,  -1,  -3,  -4,  -5,  -6,  -7,  -7,  -7,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +11, +11, +10,  +9,  +7,  +6,  +5,  +3,  +2,  +1,  +2,  +2,  +2,  +2,  +2,  +3,  +3,  +3,  +3,  +2,  +1,  +0,  -1,  -1,  +1,  +1,  +3,  +6,  +8, +10,  +6,  +9,  +7,  +6,  +6,  +5,  +2,  +0,  +0,  -1,  +0,  +0,  +1,  +2,  +2,  +3,  +4,  +7,  +8,  +8, +10, +10, +11, +11, +10,  +9,  +9, +10, +12, +12, +13, +12, +11, +10,  +9,  +9,  +8,  +7,  +7,  +7,  +6,  +6,  +4,  +3,  +2,  +0,  -2,  -3,  -5,  -6,  -7,  -7,  -7,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +9,  +7,  +6,  +5,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +3,  +4,  +4,  +3,  +3,  +2,  +1,  +0,  +0,  +0,  +1,  +2,  +4,  +6,  +8,  +9, +10,  +9,  +8,  +7,  +6,  +5,  +3,  +0,  -1,  -1,  +0,  +0,  +1,  +2,  +2,  +3,  +5,  +6,  +8,  +9, +10, +11, +11, +11, +10,  +9,  +9, +10, +11, +12, +13, +12, +12, +10,  +9,  +9,  +8,  +7,  +7,  +7,  +7,  +6,  +5,  +4,  +2,  +0,  -1,  -2,  -4,  -5,  -6,  -7,  -7,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +7,  +6,  +4,  +3,  +2,  +2,  +2,  +2,  +2,  +2,  +3,  +4,  +4,  +4,  +3, -17,  +1,  +0,  +0,  +1,  +2,  +3,  +5,  +7,  +9, +10, +10, +10,  +9,  +8,  +7,  +5,  +2,  +0,  -1,  -1,  +0,  +1,  +2,  +2,  +2,  +4,  +5,  +7,  +8,  +9, +11, +11, +11, +11, +10,  +9,  +9,  +9, +11, +12, +13, +12, +12, +10, +10,  +9,  +8,  +7,  +7,  +7,  +7,  +6,  +6,  +4,  +3,  +2,  +0,  -2,  -3,  -4,  -5,  -6,  -7,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +6,  +4,  +3,  +2,  +2, -17, -17, -17, -17,  +3,  +4,  +4,  +3, -17, -17, -17,  +0,  +0,  +1,  +2,  +4,  +5,  +8, +10, +10, +10,  +9,  +9,  +8,  +7,  +5,  +2, -17, -17, -17, -17,  +1,  +2,  +2,  +3,  +5,  +6,  +7,  +8,  +9, +10, +11, +11, +11, +11, +10,  +9,  +9, +10, +12, +13, +12, +12, +11, +10,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +6,  +5,  +4,  +2,  +0,  -1,  -2,  -4,  -5,  -6,  -7, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +3,  +2, -17, -17, -17, -17, -17, -17,  +4,  +4, -17, -17, -17, -17,  +0,  +0,  +1,  +3,  +4,  +5,  +9, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +5,  +6,  +7,  +8,  +9, +10, +11, +11, +11, +11, +10,  +9,  +9, +10, +12, +13, +12, +12, +11, +10,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +4,  +3,  +2,  +0,  -2,  -3,  -4,  -5,  -6, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +0,  +1,  +2, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +10, +11, +11, +11, +11, +10,  +9,  +9, +10, +11, +12, +12, +12, +11, +10,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +5,  +4,  +2,  +1,  -1,  -2,  -3,  -5,  -6, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +11, +11,  +9,  +9, +10, +11, +12, +13, +12, +11, +10,  +9,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +4,  +3,  +2,  +0,  -1,  -3,  -4,  -5, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  -1, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +9, +11, +12, +13, +12, +11, +10,  +9,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +5,  +4,  +2,  +1,  -1,  -2,  -3, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, +12, +11, +10, +10,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +7,  +6,  +5,  +4,  +3,  +2,  +0,  -1,  -3, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +9,  +8,  +7,  +7,  +7,  +7,  +7,  +7,  +6,  +6,  +5,  +4,  +2,  +1,  +0,  -2, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +7,  +7,  +7,  +7,  +7,  +6,  +5,  +4,  +3,  +2,  +0, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
{ -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17,  +7,  +6,  +6,  +5,  +4,  +2, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17, -17 },
};
