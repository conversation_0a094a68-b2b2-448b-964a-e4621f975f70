#pragma once
#include <windows.h>

// Apple风格UI设计系统
// 基于UI需求.md的设计规范

// ==========================================
// 颜色定义 (Apple Design System Colors)
// ==========================================

// 主要颜色
#define APPLE_WHITE             RGB(255, 255, 255)      // #FFFFFF - 主背景
#define APPLE_LIGHT_GRAY        RGB(245, 245, 247)      // #F5F5F7 - 辅助背景
#define APPLE_BLUE              RGB(0, 122, 255)        // #007AFF - iOS系统蓝
#define APPLE_TEXT_PRIMARY      RGB(29, 29, 31)         // #1D1D1F - 主文本
#define APPLE_TEXT_SECONDARY    RGB(134, 134, 139)      // #86868B - 次要文本
#define APPLE_SEPARATOR         RGB(229, 229, 234)      // #E5E5EA - 分割线

// 状态颜色
#define APPLE_SUCCESS           RGB(52, 199, 89)        // #34C759 - 成功绿色
#define APPLE_WARNING           RGB(255, 149, 0)        // #FF9500 - 警告橙色
#define APPLE_ERROR             RGB(255, 59, 48)        // #FF3B30 - 错误红色

// 交互颜色
#define APPLE_BLUE_HOVER        RGB(0, 100, 210)        // 蓝色悬停状态
#define APPLE_BLUE_PRESSED      RGB(0, 80, 180)         // 蓝色按下状态
#define APPLE_GRAY_HOVER        RGB(235, 235, 240)      // 灰色悬停状态

// ==========================================
// 尺寸和间距定义
// ==========================================

// 圆角半径
#define APPLE_CORNER_RADIUS_SMALL   4                   // 小圆角
#define APPLE_CORNER_RADIUS_MEDIUM  6                   // 中等圆角
#define APPLE_CORNER_RADIUS_LARGE   8                   // 大圆角

// 间距
#define APPLE_SPACING_SMALL         8                   // 小间距
#define APPLE_SPACING_MEDIUM        16                  // 中等间距
#define APPLE_SPACING_LARGE         24                  // 大间距

// 控件高度
#define APPLE_BUTTON_HEIGHT         32                  // 按钮标准高度
#define APPLE_INPUT_HEIGHT          28                  // 输入框标准高度

// ==========================================
// 字体定义
// ==========================================

// 字体大小 (逻辑单位)
#define APPLE_FONT_SIZE_SMALL       -11                 // 小字体
#define APPLE_FONT_SIZE_NORMAL      -12                 // 正常字体
#define APPLE_FONT_SIZE_LARGE       -14                 // 大字体
#define APPLE_FONT_SIZE_TITLE       -16                 // 标题字体

// 字体权重
#define APPLE_FONT_WEIGHT_NORMAL    400                 // 正常
#define APPLE_FONT_WEIGHT_MEDIUM    500                 // 中等
#define APPLE_FONT_WEIGHT_BOLD      600                 // 粗体

// ==========================================
// 动画参数
// ==========================================

#define APPLE_ANIMATION_DURATION    300                 // 动画时长(毫秒)
#define APPLE_BUTTON_SCALE          0.95f               // 按钮按下缩放比例

// ==========================================
// 阴影参数
// ==========================================

#define APPLE_SHADOW_BLUR           8                   // 阴影模糊半径
#define APPLE_SHADOW_OFFSET_X       0                   // 阴影X偏移
#define APPLE_SHADOW_OFFSET_Y       2                   // 阴影Y偏移
#define APPLE_SHADOW_COLOR          RGB(0, 0, 0)        // 阴影颜色
#define APPLE_SHADOW_ALPHA          20                  // 阴影透明度(0-255)

// ==========================================
// 控件样式枚举
// ==========================================

typedef enum {
    APPLE_BUTTON_PRIMARY,       // 主要按钮(填充)
    APPLE_BUTTON_SECONDARY,     // 次要按钮(描边)
    APPLE_BUTTON_TERTIARY       // 第三级按钮(文本)
} AppleButtonStyle;

typedef enum {
    APPLE_INPUT_NORMAL,         // 正常输入框
    APPLE_INPUT_FOCUSED,        // 聚焦状态
    APPLE_INPUT_ERROR           // 错误状态
} AppleInputStyle;

// ==========================================
// 工具函数声明
// ==========================================

// 创建Apple风格的画刷
HBRUSH CreateAppleBrush(COLORREF color);

// 创建Apple风格的字体
HFONT CreateAppleFont(int size, int weight, BOOL italic = FALSE);

// 优化中文文本渲染
void SetOptimalTextRenderingMode(HDC hdc);

// 绘制圆角矩形
void DrawRoundedRect(HDC hdc, RECT* rect, int radius, COLORREF fillColor, COLORREF borderColor = 0, int borderWidth = 0);

// 绘制Apple风格按钮
void DrawAppleButton(HDC hdc, RECT* rect, LPCWSTR text, AppleButtonStyle style, BOOL isHovered = FALSE, BOOL isPressed = FALSE);

// 绘制Apple风格输入框
void DrawAppleInput(HDC hdc, RECT* rect, AppleInputStyle style);

// 应用Apple风格到ListView
void ApplyAppleStyleToListView(HWND hListView);

// 应用Apple风格到按钮
void ApplyAppleStyleToButton(HWND hButton, AppleButtonStyle style = APPLE_BUTTON_PRIMARY);

// 应用Apple风格到编辑框
void ApplyAppleStyleToEdit(HWND hEdit);

// 应用Apple风格到静态文本
void ApplyAppleStyleToStatic(HWND hStatic);

// 应用Apple风格到对话框
void ApplyAppleStyleToDialog(HWND hDialog);

// 应用Apple风格窗口样式
void ApplyAppleWindowStyle(HWND hWindow);

// 自定义窗口过程，处理Apple风格绘制
LRESULT CALLBACK AppleWindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);

// 处理自绘按钮消息
void HandleOwnerDrawButton(LPDRAWITEMSTRUCT lpDrawItem);

// 动画和交互效果
void StartButtonPressAnimation(HWND hButton);
void StartButtonReleaseAnimation(HWND hButton);
void ShowHUDMessage(HWND hParent, LPCWSTR message, BOOL isError = FALSE);
void StartPulseAnimation(HWND hControl);

// 初始化Apple UI系统
void InitializeAppleUI();

// 清理Apple UI资源
void CleanupAppleUI();

// 测试Apple UI系统
BOOL TestAppleUISystem();

// ==========================================
// 全局变量声明
// ==========================================

extern HBRUSH g_hAppleWhiteBrush;
extern HBRUSH g_hAppleLightGrayBrush;
extern HBRUSH g_hAppleBlueBrush;
extern HFONT g_hAppleFont;
extern HFONT g_hAppleBoldFont;
