﻿++解决方案 'MSCeditor2' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:MSCeditor2.sln
++MSCEditor ‎(Visual Studio 2017)
i:{00000000-0000-0000-0000-000000000000}:MSCEditor
++引用
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:{950C019E-F57A-4C4E-949D-C770FB05A8AE}
++外部依赖项
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:{2B169BD9-3800-4628-9B82-FF8C2CF38B59}
++algorithm
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\ALGORITHM
++cassert
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CASSERT
++cctype
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CCTYPE
++cerrno
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CERRNO
++cfloat
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CFLOAT
++chrono
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CHRONO
++climits
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CLIMITS
++cmath
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CMATH
++codecvt
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CODECVT
++concurrencysal.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CONCURRENCYSAL.H
++crtdefs.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CRTDEFS.H
++cstddef
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CSTDDEF
++cstdint
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CSTDINT
++cstdio
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CSTDIO
++cstdlib
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CSTDLIB
++cstring
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CSTRING
++ctime
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CTIME
++cwchar
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CWCHAR
++cwctype
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CWCTYPE
++eh.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\EH.H
++exception
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\EXCEPTION
++forward_list
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\FORWARD_LIST
++fstream
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\FSTREAM
++functional
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\FUNCTIONAL
++initializer_list
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\INITIALIZER_LIST
++intrin0.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\INTRIN0.H
++ios
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\IOS
++iosfwd
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\IOSFWD
++iostream
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\IOSTREAM
++istream
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\ISTREAM
++limits
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\LIMITS
++limits.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\LIMITS.H
++list
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\LIST
++locale
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\LOCALE
++memory
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\MEMORY
++new
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\NEW
++ostream
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\OSTREAM
++ratio
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\RATIO
++sal.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\SAL.H
++sourceannotations.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\CODEANALYSIS\SOURCEANNOTATIONS.H
++stdexcept
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\STDEXCEPT
++stdint.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\STDINT.H
++streambuf
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\STREAMBUF
++string
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\STRING
++system_error
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\SYSTEM_ERROR
++thread
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\THREAD
++tuple
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\TUPLE
++type_traits
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\TYPE_TRAITS
++typeinfo
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\TYPEINFO
++unordered_map
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\UNORDERED_MAP
++use_ansi.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\USE_ANSI.H
++utility
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\UTILITY
++vadefs.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\VADEFS.H
++vcruntime.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\VCRUNTIME.H
++vcruntime_exception.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\VCRUNTIME_EXCEPTION.H
++vcruntime_new.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\VCRUNTIME_NEW.H
++vcruntime_typeinfo.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\VCRUNTIME_TYPEINFO.H
++vector
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\VECTOR
++xatomic.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XATOMIC.H
++xatomic0.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XATOMIC0.H
++xcall_once.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XCALL_ONCE.H
++xerrc.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XERRC.H
++xfacet
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XFACET
++xhash
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XHASH
++xiosbase
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XIOSBASE
++xkeycheck.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XKEYCHECK.H
++xlocale
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCALE
++xlocbuf
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCBUF
++xlocinfo
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCINFO
++xlocinfo.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCINFO.H
++xlocmes
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCMES
++xlocmon
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCMON
++xlocnum
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCNUM
++xloctime
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XLOCTIME
++xmemory
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XMEMORY
++xmemory0
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XMEMORY0
++xnode_handle.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XNODE_HANDLE.H
++xpolymorphic_allocator.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XPOLYMORPHIC_ALLOCATOR.H
++xstddef
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XSTDDEF
++xstring
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XSTRING
++xstring_insert.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XSTRING_INSERT.H
++xtgmath.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XTGMATH.H
++xthrcommon.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\THR\XTHRCOMMON.H
++xthread
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\THR\XTHREAD
++xthreads.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\THR\XTHREADS.H
++xtime
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\THR\XTIME
++xtimec.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\THR\XTIMEC.H
++xtr1common
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XTR1COMMON
++xutility
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\XUTILITY
++ymath.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\YMATH.H
++yvals.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\YVALS.H
++yvals_core.h
e:{d218110b-9f11-450b-a44b-b877eac2c3ab}:F:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.16.27023\INCLUDE\YVALS_CORE.H
++dlgprocs.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\dlgprocs.cpp
++dlgprocs.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\dlgprocs.h
++externs.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\externs.cpp
++externs.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\externs.h
++heightmap.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\heightmap.h
++main.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\main.cpp
++Main.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\Main.h
++map.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\map.cpp
++map.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\map.h
++map_dlgprocs.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\map_dlgprocs.cpp
++map_dxt.jpg
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\MapAsset\map_dxt.jpg
++msce.ico
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\msce.ico
++MSCeditor.rc
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\MSCeditor.rc
++resize.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\resize.cpp
++resize.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\resize.h
++resource.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\resource.h
++stdafx.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\stdafx.h
++structs.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\structs.h
++utils.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\utils.cpp
++utils.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\utils.h
++variable.cpp
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\variable.cpp
++variable.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\variable.h
++version.h
i:{d218110b-9f11-450b-a44b-b877eac2c3ab}:C:\Users\<USER>\Desktop\MSCEditor-f04319492f47aa9c94d447074a1be617d77427d4\MSCeditor\version.h
