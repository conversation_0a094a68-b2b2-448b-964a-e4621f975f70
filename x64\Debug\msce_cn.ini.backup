﻿// ==========================================~-
// ---> 此文件是程序正常工作所必需的 <---
// ==========================================~-
// MSCeditor读取的数据，用作预定义的传送位置和标识符。
// 需要与可执行文件在同一文件夹中，并且文件名为"msce_cn.ini"。
// 如有需要，可以添加或更改条目。程序几乎不进行任何验证，
// 所以请确保语法正确。
// ==========================================~-

// ==========================================~-
// 这些是用于地图和传送的位置
// 语法:
// "显示名称" "坐标" "地图相关"
// 注意：坐标格式为(x,(高度),y)
// 注意：如果"地图相关"未定义且为"1"，则假定与地图无关
// ==========================================~-

#"Locations"
"车库工作台"		"-14.3, 2, 1.5"
"厨房餐桌"		"-10, 0.3, 6"
"车库车道"	"-16.2, 4, 11"
"修理厂"		"1553, 7, 723" 		"1"
"泰莫商店"		"-1554, 4, 1174"	"1"
"文蒂之家"		"-173, -3, 1021"	"1"
"直线赛道"		"-1295, 4, -940"	"1"
"西尔卡之家"	"451, 4, -1333"		"1"
"越野赛道"		"-284, 4, -813"		"1"
"墓地"			"-1454, 7, 1145"	"1"
"检车站"		"-1533, 7, 1251"	"1"
"约科之家"		"1944, 8, -224"		"1"
"污水处理厂"	"-1503, 6, 1345"	"1"
"垃圾场"			"-787, 15, -645"	"1"
"草莓田"	"-1206, 2, -617"	"1"
"小木屋"			"-851, 3, 513"		"1"
"船只生成点"		"8.7, -4.5, 149"
"铁路道口" "1010, 0, -737.9"
"运动场"		"-1266, 4, 1186"	"1"
"公寓"		"-1289, 1, 1100"	"1"
"滑雪山"			"-2026, 71, -117"	"1"
"堆肥场"			"0, 4, 0"
"粮仓"		"-767, 8, 1690"		"1"
"天线"			"-330, 5, 1371"		"1"
"舞厅" 	"462, 10, 1333"		"1"
"豪宅"			"1360, 9, 796"		"1"
"泰莫之家"		"-877, 9, 1240"		"1"
"家"				"-6, 0.5, 10"		"1"
"木材工作"		"1923, 5, -420"		"1"
"厕所岛"		"-838.5, -2, 507"	"1"
"修理厂厕所"	"1566, 5, 720"		"1"
"垃圾场厕所"	"-780, 13, -647"	"1"
"泰莫厕所"		"-1549, 4, 1188"	"1"
"化粪池 1"		"-1300, 2, 1134"	"1"
"化粪池 2"		"-1357, 3, 1208"	"1"
"化粪池 3"		"1582, 4.5, 661"	"1"
"化粪池 4"		"1528, 5, 722"		"1"
"化粪池 5"		"1883, -3, -783"	"1"
"木棚"			"55, 0.5, -80"		"1"
"家居用品店"	"2183, 0, -493"		"1"
"学校"			"-1373, 6.5, 1264"	"1"
"苏斯基之家"		"-1515, 7, 1143"	"1"
"托伊沃之家"		"22.5, 0.5, -41"	"1"
"豪宅水井"		"1367, 8, 786"		"1"
"文蒂水井"		"-180, -3, 1011"	"1"
"西尔卡水井"		"447, -3, -1340"	"1"
"约科水井"		"1936, 8, -230"		"1"
"岛屿水井"		"-850, -1, 498" 	"1"
"草莓田水井"	"-1257, 1, -582" 	"1"
"凯瑟林佩拉水井" "1864, -3, -791"	"1"
"佩拉亚尔维水井" 	"-1472, 1.5, 1087"	"1"
"托瓦卡农场"		"-839, 2.5, 1333"	"1"

// ==========================================~-
// 这些是编辑器已知的物品类型！
// 语法:
// "显示名称" "物品名称" "与物品关联的属性ID" "层" "物品ID名称"
// 注意：当未设置时，物品ID名称默认为物品名称 + "ID"
// 注意：所有物品都有0:Transform属性
// ==========================================~-

#"Items"
"交流发电机皮带"		"alternator belt"	"26, 28"					"PART"	"alternatorbeltID"
"电池"				"battery"			"1, 26, 29, 30, 31, 32"		"PART"	
"啤酒箱"				"beercase"			"1, 2"						"PART"	"BeerCaseID"
"烈酒"					"booze"				"1"							"PART"	"BoozeID"
"刹车油"			"brakefluid"		"1, 3"						"PART"
"香烟"				"cigarettes"		"1"							"PART"
"咖啡"				"groundcoffee"		"35"						"PART"
"冷却液"				"coolant"			"1, 6"						"PART"
"空塑料罐"		"juiceconcentrate"	"1, 14, 15, 16, 17, 18, 19"	"ITEM"	 
"灭火器"		"fireextinguisher"	"1, 4, 26"					"ITEM"
"烧烤炭"		"grillcharcoal"		"35"						"PART"
"果汁浓缩液"		"juiceconcentrate"	"1, 8, 9, 10, 11, 12, 13" 	"ITEM"
"基尔尤酒"					"juiceconcentrate"	"1, 20, 21, 22, 23, 24, 25" "ITEM"
"灯泡"				"light bulb"		"1, 26, 28, 39"				"PART"	"lightbulbID"
"灯泡盒"			"light bulb box"	"34"						"PART"	"lightbulbboxID"
"马卡龙盒"			"macaronbox"		"1"							"PART"
"牛奶"					"milk"				"1"							"PART"
"驼鹿肉(烤)"	"moosemeat"			"36, 38"					"PART"
"驱蚊喷雾"			"mosquitospray"		"1, 4"						"PART"
"机油"			"motoroil"			"1, 5"						"PART"
"机油滤清器"			"oil filter"		"1, 26, 27, 33"				"PART"	"oilfilterID"
"梭子鱼(烤)"		"pike"				"37, 38"					"PART"
"披萨"					"pizza"				"1"							"PART"
"薯片"			"potatochips"		"1"							"PART"
"香肠"				"sausages" 			"1"							"PART"
"购物袋"			"shoppingbag"		"1"							"PART"
"火花塞"				"spark plug"		"1, 26, 27, 28, 39"			"PART"	"sparkplugID"
"火花塞盒"			"spark plug box"	"34"						"PART"	"sparkplugboxID"
"白色喷漆"		"spraycan01"		"1, 4, 40"					"ITEM"	"Spraycan01ID"
"黑色喷漆"		"spraycan02"		"1, 4, 40"					"ITEM"	"Spraycan02ID"
"红色喷漆"			"spraycan03"		"1, 4, 40"					"ITEM"	"Spraycan03ID"
"蓝色喷漆"			"spraycan04"		"1, 4, 40"					"ITEM"	"Spraycan04ID"
"棕色喷漆"		"spraycan05"		"1, 4, 40"					"ITEM"	"Spraycan05ID"
"橙色喷漆"		"spraycan06"		"1, 4, 40"					"ITEM"	"Spraycan06ID"
"黄色喷漆"		"spraycan07"		"1, 4, 40"					"ITEM"	"Spraycan07ID"
"绿色喷漆"		"spraycan08"		"1, 4, 40"					"ITEM"	"Spraycan08ID"
"浅蓝色喷漆"	"spraycan09"		"1, 4, 40"					"ITEM"	"Spraycan09ID"
"灰色喷漆"			"spraycan10"		"1, 4, 40"					"ITEM"	"Spraycan10ID"
"粉色喷漆"			"spraycan11"		"1, 4, 40"					"ITEM"	"Spraycan11ID"
"石板色喷漆"		"spraycan12"		"1, 4, 40"					"ITEM"	"Spraycan12ID"
"深灰色喷漆"	"spraycan13"		"1, 4, 40"					"ITEM"	"Spraycan13ID"
"糖"					"sugar"				"1"							"PART"
"二冲程燃油"		"twostroke"			"1, 7"						"PART"
"酵母"					"yeast"				"1"							"PART"


// ==========================================~-
// 物品属性
// 语法:
// "属性ID" "名称" "数据类型id"  "最小值" "最大值"
// 数据类型: 
// ID_ARRAY			0
// ID_TRANSFORM		1
// ID_FLOAT			2
// ID_STRING		3
// ID_BOOL			4
// ID_COLOR			5
// ID_INT			6
// ==========================================~-

#"Item_Attributes"
// 所有物品都有变换属性
"0"	 "Transform"		"1"

// 已消耗的物品将不会生成
"1"	 "Consumed"			"4"		"1"		"0"
"2"	 "Bottles"			"6"		"18"	"0"
"3"	 "Fluid"			"2"		"0"		"1"
"4"	 "Fluid"			"2"		"0"		"100"
"5"	 "Fluid"			"2"		"0"		"4"
"6"	 "Fluid"			"2"		"0"		"10"
"7"	 "Fluid"			"2"		"0"		"5"

// 果汁
"8"	 "ContainsKilju"	"4"		"-1"	"0"
"9"	 "ContainsJuice"	"4"		"0"		"1"
"10" "KiljuYeast"		"2"		"-1"	"0"
"11" "KiljuVinegar"		"2"		"-1"	"0"
"12" "KiljuSweetness"	"2"		"-1"	"0"
"13" "KiljuAlc"			"2"		"-1"	"0"

// 空果汁
"14" "ContainsKilju"	"4"		"-1"	"0"
"15" "ContainsJuice"	"4"		"-1"	"0"
"16" "KiljuYeast"		"2"		"-1"	"0"
"17" "KiljuVinegar"		"2"		"-1"	"0"
"18" "KiljuSweetness"	"2"		"-1"	"0"
"19" "KiljuAlc"			"2"		"-1"	"0"

// 基尔尤酒
"20" "ContainsKilju"	"4"		"-1"	"1"
"21" "ContainsJuice"	"4"		"-1"	"0"
"22" "KiljuYeast"		"2"		"-1"	"1"
"23" "KiljuVinegar"		"2"		"-1"	"0"
"24" "KiljuSweetness"	"2"		"-1"	"0.2"
"25" "KiljuAlc"			"2"		"-1"	"0.1362"

// 可安装在汽车上的物品
// 我们暂时将两种状态都保持为false
"26" "Installed"		"4"		"-1"	"0"
"27" "Tightness"		"2"		"-1"	"0"
"28" "Wear"				"2"		"100"	"0"

// 电池 
"29" "Discharge"		"2"		"140"	"0"
"30" "Charge"			"2"		"0"		"140"
"31" "ChargeMax"		"2"		"0"		"145"
"32" "OnCharged"		"4"		"-1"	"0"

// 机油滤清器
"33" "Dirt"				"2"		"100"	"1"

// 火花塞
"34" "Quantity"			"6"		"0"		"4"

// 咖啡
"35" "Ground"			"2"		"0"		"100"

// 木炭
"36" "Contents"			"2"		"0"		"100"

// 驼鹿肉
"37" "Decay"			"2"		"1"		"0"
"38" "Type"				"6"		"3"		"2"

"39" "TriggerID"		"6"		"-1"	"0"
"40" "ColorID"			"6"		"-1"	"0"


// ==========================================~-
// 这些用于Satsuma报告以正确识别汽车零件。
// 如果报告功能不工作，这意味着游戏可能
// 收到了更新，改变了变量的命名方式。
// 您可以通过更改此处的键来自行修复
// ==========================================~-

#"Report_Identifiers"
"bolted" // bolted，一个布尔值，我不确定它是如何工作的，但它可能很重要。
"bolts" // bolts，一个字符串列表，跟踪零件上所有螺栓的状态。仅当零件有螺栓且已安装时存在
"damaged" //damaged，一个布尔值，保存零件的损坏状态
"installed" //installed，工具用来识别汽车零件的布尔值
"tightness" //tightness，一个浮点值，是零件上所有螺栓状态的总和。仅当零件有螺栓时存在
"corner" //安装车轮的车角，未安装时为空字符串，否则为FR/FL/RR/RL

// ==========================================~-
// Satsuma报告的一些特殊情况
// "要匹配的字符串" "SCid" "参数"(可选)
// SCids:
// 0 : 通过螺栓状态的紧固度偏移
// 1 : 
// 2 : 在报告中隐藏条目
// ==========================================~-

#"Report_Special"
"oilpan" "0" "-8" //底部的大螺栓不计入紧固度，因为原因
"alternator" "0" "-8" //交流发电机旋转不计入紧固度
//"alternator"  "1" "alternatorrotation"
"motorhoist" "2" 
"fiberglasshood" "2" 
"monitor" "2"
"helmetitem" "2"
"case" "2"
"speakers" "2"
"peripherals" "2"
"warningtriangle" "2" 

// ==========================================~-
// 维护条目。
// 此列表中不包含但名称中含有"wear"的条目
// 将由程序添加。
// 星号是数字的通配符，用于物品
// 语法:
// "显示名称" "名称" "数据类型" "最小值" "最大值" "建议值"
// 数据类型: 
// ID_FLOAT			2
// ID_VECTOR		7
// ==========================================~-

#"Report_Maintenance"
"燃油量"					"fueltankfuellevel"					"2"		"0"			"36"
"发动机机油量"				"oilpanoillevel"					"2"		"0"			"3"	
"机油状况"					"oilpanoilcontamination"			"2"		"95"		"0"	
"前制动液量"		"brakemastercylinderfluidlevelf"	"2"		"0"			"1"
"后制动液量"			"brakemastercylinderfluidlevelr"	"2"		"0"			"1"
"离合器液量"			"clutchmastercylinderfluidlevel"	"2"		"0"			"0.5"
"竞赛散热器冷却液量"	"racingradiatorwater"				"2"		"0"			"7"
"标准散热器冷却液量"	"radiatorwater"						"2"		"0"			"5.4"
"凸轮轴对准"			"camshaftgearangle"					"2"		"0"			"0"			"0"
"左前轮对准"	"steeringrodflalignment"			"2"		"0"			"0"			"0"
"右前轮对准"	"steeringrodfralignment"			"2"		"0"			"0"			"0"
"气缸进气 1"				"rockershaftcyl1intake"				"2"		"7.75"		"8"			"8"
"气缸进气 2"				"rockershaftcyl2intake"				"2"		"7.75"		"8"			"8"
"气缸进气 3"				"rockershaftcyl3intake"				"2"		"7.75"		"8"			"8"
"气缸进气 4"				"rockershaftcyl4intake"				"2"		"7.75"		"8"			"8"
"气缸排气 1"			"rockershaftcyl1exhaust"			"2"		"6.75"		"8"			"7"
"气缸排气 2"			"rockershaftcyl2exhaust"			"2"		"6.75"		"8"			"7"
"气缸排气 3"			"rockershaftcyl3exhaust"			"2"		"6.75"		"8"			"7"
"气缸排气 4"			"rockershaftcyl4exhaust"			"2"		"6.75"		"8"			"7"
"标准化油器空燃比"	"carburatoridleadjust"				"2"		"10"		"16.75"		"14.7"
"双化油器空燃比"	"twincarburatorsidleadjust"			"2"		"10"		"16.75"		"14.7"
"竞赛化油器空燃比 1"	"racingcarburatorsadjust1"			"2"		"10"		"17"		"14.9"
"竞赛化油器空燃比 2"	"racingcarburatorsadjust2"			"2"		"10"		"17"		"14.9"
"竞赛化油器空燃比 3"	"racingcarburatorsadjust3"			"2"		"10"		"17"		"14.9"
"竞赛化油器空燃比 4"	"racingcarburatorsadjust4"			"2"		"10"		"17"		"14.9"
"最终齿轮比"				"gearboxfinalgearratio"				"2"		""			"3.7"
"分电器点火正时"		"distributorsparkangle"				"2"		"12"		"15"		"14.5"
"风扇皮带松紧度"				"alternatorrotation"				"2"		"7"			"7"			"7"
"悬挂状况 左前"		"wheelfldamagevector"				"7"		""			""			"-0, 0, 0"		
"悬挂状况 右前"		"wheelfrdamagevector"				"7"		""			""			"-0, 0, 0"	
"悬挂状况 左后"		"wheelrldamagevector"				"7"		""			""			"-0, 0, 0"		
"悬挂状况 右后"		"wheelrrdamagevector"				"7"		""			""			"-0, 0, 0"	
"风扇皮带状况(已弃用)"	"fanbeltwear"						"2"		"100"		"0"
"风扇皮带状况"				"alternatorbelt*wear"				"2"		"100"		"0"
"电池充电"				"battery*charge"					"2"		"0"			"140"
"电池容量"				"battery*chargemax"					"2"		"0"			"145"
"机油滤清器脏污"				"oilfilter*dirt"					"2"		"100"		"1"
"机油滤清器紧固度"			"oilfilter*tightness"				"2"		"0"			"8"
"灭火器内容物"		"fireextinguisher*fluid"			"2"		"0"			"100"

// ==========================================~-
// 事件时间表
// ==========================================~-

#"Event_Timetable"
"约克搭车电话"	"02 - 03"		"任意"
"日出"			"06 - 07"		"任意"
"约克基尔尤酒时间"	"06 - 22"		"任意"
"修理厂"		"08 - 16"		"周一 - 周五"
"检车站"	"08 - 16"		"周一 - 周五"
"污水处理厂"	"08 - 16"		"周一 - 周五"
"奶奶茶时间"	"08 - 16"		"任意"
"拉力赛第1天"		"10 - 18"		"周六"
"拉力赛第2天"		"10 - 18"		"周日"
"泰莫商店"		"10 - 20"		"周一 - 周六"
"足球孩子们"		"10 - 22"		"任意"
"舞厅"	"20 - 02"		"周六"
"纳波酒吧"			"20 - 02"		"周一 - 周六"
"日落"			"22 - 23"		"任意"

// ==========================================~-
// 设置
// ==========================================~-

#"Settings"
"make_backup" "1"
"backup_change_notified" "1"
"check_updates" "1"
"first_startup" "0"
"allow_scale" "0"
"use_euler" "0"
"raw_names" "1"
"check_issues" "0"
"start_with_map" "0" 
