// Microsoft Visual C++ generated resource script.
//
#include "resource.h"
#include "resize.h"
#include "version.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "winres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Chinese (Simplified) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_MAIN DIALOGEX 0, 0, 575, 251
STYLE DS_SETFONT | DS_SETFOREGROUND | DS_FIXEDSYS | DS_CENTERMOUSE | WS_MINIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
MENU IDR_MENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
	DIALOGRESIZECONTROL{ 0, 0, 100, 100 }
	CONTROL         "", IDC_List2, "SysListView32", LVS_REPORT | LVS_SINGLESEL | LVS_ALIGNLEFT | LVS_NOSORTHEADER | WS_BORDER | WS_TABSTOP, 203, 18, 365, 214
	DIALOGRESIZECONTROL{ 0, 0, 0, 0 }
	CONTROL         "条目:", IDC_STXT1, "Static", SS_SIMPLE | WS_GROUP, 206, 9, 26, 8
	DIALOGRESIZECONTROL{ 0, 0, 0, 100 }
	CONTROL         "", IDC_List, "SysListView32", LVS_REPORT | LVS_SINGLESEL | LVS_SHOWSELALWAYS | LVS_ALIGNLEFT | LVS_NOCOLUMNHEADER | WS_BORDER | WS_TABSTOP, 7, 7, 189, 207
	DIALOGRESIZECONTROL{ 0, 100, 100, 0 }
	EDITTEXT        IDC_OUTPUTBAR1, -3, 239, 202, 14, ES_AUTOHSCROLL | ES_READONLY | WS_DISABLED
	DIALOGRESIZECONTROL{ 25, 100, 100, 0 }
	EDITTEXT        IDC_OUTPUTBAR2, 198, 239, 57, 14, ES_AUTOHSCROLL | ES_READONLY | WS_DISABLED
	DIALOGRESIZECONTROL{ 25, 100, 100, 0 }
	EDITTEXT        IDC_OUTPUTBAR3, 254, 239, 107, 14, ES_AUTOHSCROLL | ES_READONLY | WS_DISABLED
	DIALOGRESIZECONTROL{ 100, 100, 100, 0 }
	EDITTEXT        IDC_OUTPUTBAR4, 360, 239, 216, 14, ES_AUTOHSCROLL | ES_READONLY | WS_DISABLED
	DIALOGRESIZECONTROL{ 0, 100, 0, 0 }
	CONTROL         "", IDC_OUTPUT1, "Static", SS_SIMPLE | WS_GROUP, 2, 241, 174, 8
	DIALOGRESIZECONTROL{ 25, 100, 0, 0 }
	CONTROL         "", IDC_OUTPUT2, "Static", SS_SIMPLE | WS_GROUP, 202, 241, 50, 8
	DIALOGRESIZECONTROL{ 25, 100, 0, 0 }
	CONTROL         "", IDC_OUTPUT3, "Static", SS_SIMPLE | WS_GROUP, 258, 241, 100, 8
	DIALOGRESIZECONTROL{ 100, 100, 0, 0 }
	CONTROL         "", IDC_OUTPUT4, "Static", SS_SIMPLE | WS_GROUP, 363, 241, 209, 8
	DIALOGRESIZECONTROL{ 0, 100, 0, 0 }
	EDITTEXT        IDC_FILTER, 37, 219, 159, 13, ES_LOWERCASE
	DIALOGRESIZECONTROL{ 0, 100, 0, 0 }
	CONTROL         "过滤:", IDC_STXT2, "Static", SS_SIMPLE | WS_GROUP, 14, 221, 20, 7
END

IDD_TRANSFORM DIALOGEX 0, 0, 309, 172
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "编辑变换"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_LOC,25,25,259,14,ES_AUTOHSCROLL | ES_WANTRETURN,WS_EX_CLIENTEDGE
    GROUPBOX        "位置 ( x , y , z )",IDC_STATIC,16,15,276,31
    EDITTEXT        IDC_ROT,25,65,259,14,ES_AUTOHSCROLL | ES_WANTRETURN,WS_EX_CLIENTEDGE
    GROUPBOX        "",IDC_ROTBOX,15,54,276,31
    EDITTEXT        IDC_SCA,25,105,259,14,ES_AUTOHSCROLL | ES_WANTRETURN,WS_EX_CLIENTEDGE
    GROUPBOX        "缩放 ( x , y , z )",IDC_STATIC,15,94,276,31
    PUSHBUTTON      "应用",IDC_APPLY,185,129,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,241,129,50,14
    EDITTEXT        IDC_TAG,50,129,133,14,ES_AUTOHSCROLL
    LTEXT           "层级:",IDC_STATIC,23,131,20,8
END

IDD_COLOR DIALOGEX 0, 0, 323, 94
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "编辑颜色"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_COL,27,26,259,14,ES_AUTOHSCROLL | ES_WANTRETURN,WS_EX_CLIENTEDGE
    GROUPBOX        "颜色 ( r , g , b , a )",IDC_STATIC,17,16,276,31
    PUSHBUTTON      "应用",IDC_APPLY,187,55,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,243,55,50,14
END

IDD_ABOUT DIALOGEX 0, 0, 173, 115
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "关于"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "关闭",IDOK,89,66,50,14
    ICON            IDI_ICON1,IDC_CICON,47,7,21,20
    LTEXT           "",IDC_VERSION,75,10,79,8
    LTEXT           "by durkhaz",IDC_STATIC,75,18,36,8
    CTEXT           "请报告Bug并提出建议\n以帮助改进程序！\n\n感谢使用MSCeditor！",IDC_STATIC,7,29,148,34
    DEFPUSHBUTTON   "提供反馈*",IDC_FEEDBACK,19,66,64,14
    LTEXT           "(* 打开Steam社区帖子)",IDC_STATIC,43,83,112,8
END

IDD_STRING DIALOGEX 0, 0, 515, 332
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "编辑数组"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    PUSHBUTTON      "应用",IDC_APPLY,395,292,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,450,292,50,14
    EDITTEXT        IDC_STRINGEDIT,7,7,493,281,ES_MULTILINE | ES_AUTOHSCROLL | WS_VSCROLL | WS_HSCROLL,WS_EX_CLIENTEDGE
END

IDD_TELEPORT DIALOGEX 0, 0, 149, 150
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "传送"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    COMBOBOX        IDC_LOCFROM,7,18,128,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "传送什么？",IDC_STATIC,7,7,58,8
    COMBOBOX        IDC_LOCTO,7,50,128,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "传送到哪里？",IDC_STATIC,7,39,62,8
    PUSHBUTTON      "应用",IDC_APPLY,17,108,50,14,WS_DISABLED
    PUSHBUTTON      "取消",IDC_DISCARD,69,108,50,14
    CONTROL         "保持原始旋转",IDC_TELEC,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,58,91,84,10
    EDITTEXT        IDC_OFFSET,7,73,60,12,ES_AUTOHSCROLL,WS_EX_RIGHT
    LTEXT           "高度偏移",IDC_STATIC,69,75,43,8
END

IDD_BOLTS DIALOGEX 0, 0, 509, 274
STYLE DS_SETFONT | DS_FIXEDSYS | DS_CONTROL | WS_CHILD
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_BLIST,"SysListView32",LVS_REPORT | LVS_SINGLESEL | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,8,8,285,260
    CONTROL         "",IDC_BT1,"Static",SS_SIMPLE | WS_GROUP,315,21,170,8
    GROUPBOX        "螺栓概览:",IDC_STATIC,301,7,192,57
    CONTROL         "",IDC_BT2,"Static",SS_SIMPLE | WS_GROUP,315,34,170,8
    CONTROL         "",IDC_BT3,"Static",SS_SIMPLE | WS_GROUP,315,47,170,8
    CONTROL         "",IDC_BT4,"Static",SS_SIMPLE | WS_GROUP,315,82,170,8
    GROUPBOX        "零件概览:",IDC_STATIC,301,68,192,85
    CONTROL         "",IDC_BT5,"Static",SS_SIMPLE | WS_GROUP,315,95,170,8
    CONTROL         "",IDC_BT6,"Static",SS_SIMPLE | WS_GROUP,315,108,170,8
    CONTROL         "",IDC_BT7,"Static",SS_SIMPLE | WS_GROUP,315,134,170,8
    CONTROL         "",IDC_BT8,"Static",SS_SIMPLE | WS_GROUP,315,121,170,8
    GROUPBOX        "操作:",IDC_STATIC,301,157,192,110
    PUSHBUTTON      "修复松动螺栓",IDC_BBUT1,315,172,78,14
    PUSHBUTTON      "修复所有零件",IDC_BBUT6,315,192,78,14
    PUSHBUTTON      "修复卡住零件",IDC_BBUT3,315,212,78,14
    PUSHBUTTON      "松开所有螺栓",IDC_BBUT2,401,172,78,14
    PUSHBUTTON      "修复车身",IDC_BBUT4,401,192,78,14
    PUSHBUTTON      "安装电路",IDC_BBUT5,401,212,78,14
    LTEXT           "修复零件不会影响磨损。请查看维护选项卡。",IDC_STATIC,303,256,189,9,0,WS_EX_RIGHT
END

IDD_SPAWNITEM DIALOGEX 0, 0, 149, 152
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "生成物品"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    COMBOBOX        IDC_SPAWNWHAT,7,18,128,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "生成什么？",IDC_STATIC,7,7,53,8
    COMBOBOX        IDC_LOCTO,7,52,128,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "在哪里生成？",IDC_STATIC,7,41,57,8
    LTEXT           "生成多少个？",IDC_STATIC,7,75,69,8
    EDITTEXT        IDC_AMOUNT,7,87,128,13,ES_AUTOHSCROLL | ES_NUMBER
    PUSHBUTTON      "应用",IDC_APPLY,19,110,50,14,WS_DISABLED
    PUSHBUTTON      "取消",IDC_DISCARD,71,110,50,14
END

IDD_CLEANITEMS DIALOGEX 0, 0, 151, 98
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "清理物品"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    COMBOBOX        IDC_CLEANWHAT,7,18,128,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "清理什么？",IDC_STATIC,7,7,59,8
    CONTROL         "",IDC_IT,"Static",SS_SIMPLE | WS_GROUP,7,40,127,8
    PUSHBUTTON      "应用",IDC_APPLY,19,56,50,14,WS_DISABLED
    PUSHBUTTON      "取消",IDC_DISCARD,71,56,50,14
END

IDD_HELP DIALOGEX 0, 0, 173, 110
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "帮助"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "不，谢谢！",IDOK,89,62,50,14
    CTEXT           "详细指南可以在\nMy Summer Car Steam社区中找到。",-1,7,43,148,19
    DEFPUSHBUTTON   "带我去那里！ *",IDC_FEEDBACK,19,62,64,14
    LTEXT           "(* 打开Steam社区)",-1,49,78,89,8
    LTEXT           "",IDC_HELPNEW,25,34,115,10
END

IDD_REPORT DIALOGEX 0, 0, 439, 176
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "Satsuma报告"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    PUSHBUTTON      "关闭",IDC_DISCARD,157,60,50,14
    LTEXT           " 重要提示：你可以拥有多个的汽车零件存储在'items.txt'中。(例如电池、火花塞等) ",IDC_DISCARDTEXT,37,100,395,8
END

IDD_MAINTENANCE DIALOGEX 0, 0, 309, 176
STYLE DS_SETFONT | DS_FIXEDSYS | DS_CONTROL | WS_CHILD | WS_CAPTION
CAPTION "维护"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
END

IDD_VECTOR DIALOGEX 0, 0, 323, 94
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "编辑向量"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_COL,27,26,259,14,ES_AUTOHSCROLL | ES_WANTRETURN,WS_EX_CLIENTEDGE
    GROUPBOX        "向量 ( x , y , z )",IDC_STATIC,17,16,276,31
    PUSHBUTTON      "应用",IDC_APPLY,187,55,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,243,55,50,14
END

IDD_KEYS DIALOGEX 0, 0, 205, 189
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "钥匙"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDC_APPLY,88,146,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,141,146,50,14
END

IDD_BLANK DIALOGEX 0, 0, 312, 143
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_BORDER | WS_VSCROLL
EXSTYLE 0x800000L
FONT 8, "", 400, 0, 0x1
BEGIN
END

IDD_TIMEWEATHER DIALOGEX 0, 0, 361, 189
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "时间和天气"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LTEXT           "时间: (24小时制)",IDC_STATIC,25,25,70,8
    COMBOBOX        IDC_TTIME,25,36,87,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "星期:",IDC_STATIC,25,60,16,8
    COMBOBOX        IDC_TDAY,25,71,88,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "天气: (玩家处)",IDC_STATIC,25,95,68,8
    COMBOBOX        IDC_TWEATHER,25,106,88,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "应用",IDC_APPLY,18,136,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,70,136,50,14
    GROUPBOX        "操作:",IDC_STATIC,7,7,124,153
    GROUPBOX        "时间表:",IDC_STATIC,135,7,212,153
    CONTROL         "",IDC_TTIMETABLE,"SysListView32",LVS_REPORT | LVS_SINGLESEL | LVS_ALIGNLEFT | LVS_NOSORTHEADER | WS_BORDER,141,18,196,132
END

IDD_CONTAINER DIALOGEX 0, 0, 515, 332
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "编辑数组"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    PUSHBUTTON      "应用",IDC_APPLY,395,292,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,450,292,50,14
    CONTROL         "",IDC_CONTAINERLIST,"SysListView32",LVS_REPORT | LVS_SINGLESEL | LVS_ALIGNLEFT | WS_BORDER,7,7,492,278
    LTEXT           "点击条目修改它，然后点击设置。要添加或删除条目，请点击索引。",IDC_STATIC,7,293,343,8
END

IDD_MAP DIALOGEX 0, 0, 942, 487
STYLE DS_SETFONT | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "地图"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
END

IDD_BLANKMAP DIALOGEX 0, 0, 128, 128
STYLE WS_CHILD
BEGIN
END

IDD_COMPAREOPTIONS DIALOGEX 0, 0, 235, 223
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CONTROL | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "比较选项"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "比较",IDC_APPLY,57,182,50,14
    PUSHBUTTON      "取消",IDC_DISCARD,121,182,50,14
    GROUPBOX        "包含的数据类型",IDC_STATIC,15,15,197,46
    CONTROL         "变换",IDC_COMP_BOX_TR,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,21,28,43,10
    CONTROL         "浮点",IDC_COMP_BOX_FL,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,68,28,32,10
    CONTROL         "字符串",IDC_COMP_BOX_ST,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,115,28,35,10
    CONTROL         "布尔值",IDC_COMP_BOX_BO,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,162,28,41,10
    CONTROL         "颜色",IDC_COMP_BOX_CO,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,21,42,33,10
    CONTROL         "整数",IDC_COMP_BOX_IN,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,68,42,39,10
    CONTROL         "三维向量",IDC_COMP_BOX_VE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,115,42,41,10
    CONTROL         "未知",IDC_COMP_BOX_UN,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,162,42,45,10
    EDITTEXT        IDC_COMP_EDIT_FI,22,83,183,14,ES_AUTOHSCROLL
    GROUPBOX        "只比较名称包含以下内容的键",IDC_STATIC,15,68,197,47
    LTEXT           "例如 player",IDC_STATIC,169,100,36,8
    GROUPBOX        "杂项",IDC_STATIC,15,121,197,51
    CONTROL         "包含容器数据类型 (例如 List 等)",IDC_COMP_BOX_DT,
                    "Button",BS_AUTOCHECKBOX | WS_TABSTOP,21,135,155,10
    EDITTEXT        IDC_COMP_EDIT_FL,95,149,75,14,ES_AUTOHSCROLL
    LTEXT           "浮点差异阈值:",IDC_STATIC,21,152,70,8
END

IDD_LIYUE DIALOGEX 0, 0, 173, 115
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTERMOUSE | WS_CAPTION | WS_SYSMENU
CAPTION "璃月汉化版"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "关闭",IDOK,61,94,50,14
    CTEXT           "MSCEditor 中文汉化\n\n汉化完成度：95%\n\n感谢使用MSCEditor璃月汉化版！",IDC_LIYUE_TEXT,7,7,159,80
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDD_MAIN, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 568
        TOPMARGIN, 7
        BOTTOMMARGIN, 244
    END

    IDD_TRANSFORM, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
        BOTTOMMARGIN, 164
    END

    IDD_COLOR, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 316
        TOPMARGIN, 7
        BOTTOMMARGIN, 86
    END

    IDD_ABOUT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 166
        TOPMARGIN, 7
        BOTTOMMARGIN, 108
    END

    IDD_STRING, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 508
        TOPMARGIN, 7
        BOTTOMMARGIN, 325
    END

    IDD_TELEPORT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 142
        TOPMARGIN, 7
        BOTTOMMARGIN, 143
    END

    IDD_BOLTS, DIALOG
    BEGIN
        LEFTMARGIN, 8
        RIGHTMARGIN, 502
        TOPMARGIN, 7
        BOTTOMMARGIN, 273
    END

    IDD_SPAWNITEM, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 142
        TOPMARGIN, 7
        BOTTOMMARGIN, 145
    END

    IDD_CLEANITEMS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 144
        TOPMARGIN, 7
        BOTTOMMARGIN, 91
    END

    IDD_HELP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 166
        TOPMARGIN, 7
        BOTTOMMARGIN, 103
    END

    IDD_REPORT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 432
        TOPMARGIN, 7
        BOTTOMMARGIN, 169
    END

    IDD_MAINTENANCE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
        BOTTOMMARGIN, 169
    END

    IDD_VECTOR, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
    END

    IDD_KEYS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 198
        TOPMARGIN, 7
        BOTTOMMARGIN, 182
    END

    IDD_BLANK, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
    END

    IDD_TIMEWEATHER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 354
        TOPMARGIN, 7
        BOTTOMMARGIN, 182
    END

    IDD_CONTAINER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 508
        TOPMARGIN, 7
        BOTTOMMARGIN, 325
    END

    IDD_MAP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_BLANKMAP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_COMPAREOPTIONS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 228
        TOPMARGIN, 7
        BOTTOMMARGIN, 216
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// AFX_DIALOG_LAYOUT
//

IDD_MAIN AFX_DIALOG_LAYOUT
BEGIN
    0,
    0, 0, 100, 100,
    0, 0, 0, 0,
    0, 0, 0, 100,
    0, 100, 100, 0,
    25, 100, 100, 0,
    25, 100, 100, 0,
    100, 100, 100, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 100, 0, 0,
    0, 100, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0,
    0, 0, 0, 0
END

IDD_TRANSFORM AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_COLOR AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_ABOUT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_STRING AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_TELEPORT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_BOLTS AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SPAWNITEM AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CLEANITEMS AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_HELP AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_REPORT AFX_DIALOG_LAYOUT
BEGIN
    0,
    100, 100, 0, 0,
    0, 0, 0, 0
END

IDD_MAINTENANCE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_VECTOR AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_KEYS AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_BLANK AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_TIMEWEATHER AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CONTAINER AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_MAP AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_BLANKMAP AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_COMPAREOPTIONS AFX_DIALOG_LAYOUT
BEGIN
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

IDR_MENU MENU
BEGIN
    POPUP "文件"
    BEGIN
        MENUITEM "打开",                        ID_FILE_OPEN
        MENUITEM "保存",                        ID_FILE_SAVE, GRAYED
        MENUITEM "关闭",                       ID_FILE_CLOSE, GRAYED
        MENUITEM SEPARATOR
        MENUITEM "在资源管理器中打开",            ID_FILE_EXPLORER, GRAYED
        MENUITEM "启动游戏",                 ID_FILE_GAME
        MENUITEM "启动游戏 (steam)",         ID_FILE_GAMESTEAM
        MENUITEM SEPARATOR
        MENUITEM "退出",                        ID_FILE_EXIT
    END
    POPUP "工具"
    BEGIN
        MENUITEM "传送对象",             ID_TOOLS_TELEPORTENTITY, GRAYED
        MENUITEM "Satsuma报告",              ID_TOOLS_REPORT, GRAYED
        MENUITEM "管理钥匙",                 ID_TOOLS_MANAGEKEYS, GRAYED
        MENUITEM "时间和天气",            ID_TOOLS_TIMEANDWEATHER, GRAYED
        POPUP "物品", INACTIVE
        BEGIN
            MENUITEM "生成物品",                 ID_ITEMS_SPAWNITEMS
            MENUITEM "清理物品",              ID_ITEMS_CLEANUPITEMS
        END
        MENUITEM SEPARATOR
        MENUITEM "与存档比较",             ID_TOOLS_COMPARE, GRAYED
        MENUITEM "问题报告",                ID_TOOLS_ISSUEDIAGNOSTICS, GRAYED
        MENUITEM "世界地图",                   ID_TOOLS_WORLDMAP, GRAYED
    END
    POPUP "选项"
    BEGIN
        MENUITEM "创建备份",                 ID_OPTIONS_MAKEBACKUP, CHECKED
        MENUITEM "检查更新",           ID_OPTIONS_CHECKFORUPDATES, CHECKED
        MENUITEM "使用欧拉角",            ID_OPTIONS_USEEULERANGLES, CHECKED
        MENUITEM "显示原始名称",           ID_OPTIONS_DISPLAYRAWNAMES, CHECKED
        MENUITEM "保存时检查问题",    ID_OPTIONS_CHECKISSUESWHENSAVING, CHECKED
    END
    POPUP "?"
    BEGIN
        MENUITEM "帮助",                        ID__HELP
        MENUITEM "关于",                       ID__ABOUT
        MENUITEM "璃月0v0",                    ID__LIYUE
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_ICON1               ICON                    "msce.ico"

#endif    // Chinese (Simplified) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// English resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE LANG_ENGLISH, SUBLANG_NEUTRAL

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE
BEGIN
"resource.h\0"
END

2 TEXTINCLUDE
BEGIN
"#include ""winres.h""\r\n"
"\0"
END

3 TEXTINCLUDE
BEGIN
"\r\n"
"\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
FILEVERSION VER_FILE_VERSION
PRODUCTVERSION VER_PRODUCT_VERSION
FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
FILEFLAGS 0x1L
#else
FILEFLAGS 0x0L
#endif
FILEOS 0x40004L
FILETYPE VER_FILETYPE
FILESUBTYPE 0x0L
BEGIN
BLOCK "StringFileInfo"
BEGIN
BLOCK "000904b0"
BEGIN
VALUE "FileDescription", VER_FILE_DESCRIPTION_STR "\0"
VALUE "FileVersion", VER_FILE_VERSION_STR "\0"
VALUE "InternalName", VER_INTERNAL_NAME_STR "\0"
VALUE "LegalCopyright", VER_COPYRIGHT_STR "\0"
VALUE "OriginalFilename", VER_ORIGINAL_FILENAME_STR "\0"
VALUE "ProductName", VER_PRODUCTNAME_STR
VALUE "ProductVersion", VER_PRODUCT_VERSION_STR "\0"
END
END
BLOCK "VarFileInfo"
BEGIN
VALUE "Translation", 0x9, 1200
END
END


/////////////////////////////////////////////////////////////////////////////
//
// JPG
//
#ifdef _MAP
IDR_MAPJPG                JPG                     "MapAsset\\map_dxt.jpg"
#endif

/////////////////////////////////////////////////////////////////////////////
//
// TEXT
//

#ifdef _EMBED_INI
IDR_CONFIG_INI           TEXT                    "msce_cn.ini"
#endif
#endif    // English resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED